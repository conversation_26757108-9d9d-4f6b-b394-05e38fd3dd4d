#!/usr/bin/env python3
"""
Comprehensive South African Student Accommodation Scraper
Covers ALL major universities across all 9 provinces
"""

import argparse
import logging
import sys
import os
import time
import json
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import comprehensive scraper
from scrapers.student24_comprehensive_scraper import Student24ComprehensiveScraper, SA_PROVINCES, SA_UNIVERSITIES
from utils.data_processor import DataProcessor


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration"""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Setup logging
    log_file = log_dir / f"comprehensive_scraper_{time.strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Comprehensive scraper logging initialized. Log file: {log_file}")


class ComprehensiveAccommodationScraper:
    """Comprehensive scraper for all South African universities"""

    def __init__(self, delay: float = 2, max_retries: int = 3):
        self.delay = delay
        self.max_retries = max_retries
        self.logger = logging.getLogger(__name__)
        
        # Initialize comprehensive scraper
        self.scraper = Student24ComprehensiveScraper(delay=delay, max_retries=max_retries)
    
    def scrape_all_accommodations(self, provinces: List[str] = None, universities: List[str] = None) -> List[Dict[str, Any]]:
        """Scrape accommodations with optional filtering"""

        self.logger.info("Starting comprehensive student accommodation scraping")
        
        if provinces:
            self.logger.info(f"Filtering by provinces: {provinces}")
        if universities:
            self.logger.info(f"Filtering by universities: {universities}")

        try:
            # Get university summary
            summary = self.scraper.get_university_summary()
            self.logger.info(f"Total coverage: {summary['total_accommodations']} accommodations, "
                           f"{len(summary['universities'])} universities, {len(summary['provinces'])} provinces")

            # Scrape accommodations
            accommodations = self.scraper.scrape_accommodations(provinces=provinces, universities=universities)
            
            self.logger.info(f"Successfully scraped {len(accommodations)} accommodations")
            return accommodations

        except Exception as e:
            self.logger.error(f"Error during comprehensive scraping: {e}")
            return []

    def process_and_save_data(self, accommodations: List[Dict[str, Any]], output_prefix: str = "comprehensive") -> Dict[str, Any]:
        """Process and save accommodation data"""
        
        if not accommodations:
            self.logger.warning("No accommodations to process")
            return {}

        try:
            # Create output directory
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Validate data
            self.logger.info("Validating accommodation data...")
            valid_accommodations = []
            
            for accommodation in accommodations:
                if DataProcessor.validate_listing(accommodation):
                    valid_accommodations.append(accommodation)
                else:
                    self.logger.warning(f"Invalid accommodation data: {accommodation.get('title', 'Unknown')}")
            
            self.logger.info(f"Validated {len(valid_accommodations)}/{len(accommodations)} accommodations")
            
            # Generate comprehensive report
            report = DataProcessor.generate_report(valid_accommodations)
            
            # Group by province and university
            by_province = {}
            by_university = {}
            
            for acc in valid_accommodations:
                province = acc.get('province', 'Unknown')
                university = acc.get('university', 'Unknown')
                
                if province not in by_province:
                    by_province[province] = []
                by_province[province].append(acc)
                
                if university not in by_university:
                    by_university[university] = []
                by_university[university].append(acc)
            
            # Create comprehensive data structure
            comprehensive_data = {
                "accommodations": valid_accommodations,
                "summary": {
                    "total_accommodations": len(valid_accommodations),
                    "provinces_covered": len(by_province),
                    "universities_covered": len(by_university),
                    "price_range": {
                        "min": min(acc['price'] for acc in valid_accommodations) if valid_accommodations else 0,
                        "max": max(acc['price'] for acc in valid_accommodations) if valid_accommodations else 0,
                        "average": sum(acc['price'] for acc in valid_accommodations) / len(valid_accommodations) if valid_accommodations else 0
                    }
                },
                "by_province": {
                    province: {
                        "count": len(accs),
                        "accommodations": [{"title": acc["title"], "price": acc["price"], "university": acc["university"]} for acc in accs]
                    }
                    for province, accs in by_province.items()
                },
                "by_university": {
                    university: {
                        "count": len(accs),
                        "accommodations": [{"title": acc["title"], "price": acc["price"], "location": acc["location"]["name"]} for acc in accs]
                    }
                    for university, accs in by_university.items()
                },
                "data_quality_report": report,
                "metadata": {
                    "scraped_at": datetime.now().isoformat(),
                    "scraper": "student24_comprehensive",
                    "coverage": "all_major_sa_universities",
                    "validation_rate": f"{len(valid_accommodations)}/{len(accommodations)}"
                }
            }
            
            # Save JSON data
            json_file = output_dir / f"{output_prefix}_accommodations_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_data, f, indent=2, ensure_ascii=False)
            
            # Save CSV data
            csv_file = output_dir / f"{output_prefix}_accommodations_{timestamp}.csv"
            DataProcessor.save_to_csv(valid_accommodations, csv_file)
            
            # Save summary report
            report_file = output_dir / f"{output_prefix}_report_{timestamp}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "summary": comprehensive_data["summary"],
                    "by_province": comprehensive_data["by_province"],
                    "by_university": comprehensive_data["by_university"],
                    "data_quality": report,
                    "metadata": comprehensive_data["metadata"]
                }, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Data saved successfully:")
            self.logger.info(f"  JSON: {json_file}")
            self.logger.info(f"  CSV: {csv_file}")
            self.logger.info(f"  Report: {report_file}")
            
            return comprehensive_data

        except Exception as e:
            self.logger.error(f"Error processing and saving data: {e}")
            return {}

    def close(self):
        """Clean up resources"""
        if self.scraper:
            self.scraper.close()


def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(description="Comprehensive South African Student Accommodation Scraper")
    
    parser.add_argument("--provinces", nargs="+", choices=SA_PROVINCES,
                       help="Filter by specific provinces")
    parser.add_argument("--universities", nargs="+", 
                       help="Filter by specific universities (partial matching)")
    parser.add_argument("--delay", type=float, default=2.0,
                       help="Delay between requests in seconds (default: 2.0)")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="Logging level")
    parser.add_argument("--output-prefix", default="comprehensive",
                       help="Prefix for output files")
    parser.add_argument("--list-coverage", action="store_true",
                       help="List all universities and provinces covered")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    if args.list_coverage:
        print("🇿🇦 COMPREHENSIVE COVERAGE")
        print("=" * 50)
        print(f"Provinces covered ({len(SA_PROVINCES)}):")
        for province in SA_PROVINCES:
            print(f"  📍 {province}")
        
        print(f"\nUniversities covered ({len(SA_UNIVERSITIES)}):")
        for university in SA_UNIVERSITIES:
            print(f"  🏫 {university}")
        
        return
    
    try:
        # Initialize comprehensive scraper
        logger.info("🇿🇦 Starting Comprehensive South African Student Accommodation Scraper")
        
        scraper = ComprehensiveAccommodationScraper(delay=args.delay)
        
        # Show what will be scraped
        if args.provinces:
            logger.info(f"Filtering by provinces: {args.provinces}")
        if args.universities:
            logger.info(f"Filtering by universities: {args.universities}")
        
        # Scrape accommodations
        accommodations = scraper.scrape_all_accommodations(
            provinces=args.provinces,
            universities=args.universities
        )
        
        if accommodations:
            logger.info(f"✅ Successfully scraped {len(accommodations)} accommodations")
            
            # Process and save data
            comprehensive_data = scraper.process_and_save_data(accommodations, args.output_prefix)
            
            if comprehensive_data:
                # Print summary
                summary = comprehensive_data["summary"]
                print("\n🎉 SCRAPING COMPLETED SUCCESSFULLY!")
                print("=" * 50)
                print(f"📊 Total accommodations: {summary['total_accommodations']}")
                print(f"📍 Provinces covered: {summary['provinces_covered']}")
                print(f"🏫 Universities covered: {summary['universities_covered']}")
                print(f"💰 Price range: R{summary['price_range']['min']:,.0f} - R{summary['price_range']['max']:,.0f}")
                print(f"💰 Average price: R{summary['price_range']['average']:,.0f}")
                
                print(f"\n📁 Output files saved with prefix: {args.output_prefix}")
                print("✅ Ready for production use!")
            else:
                logger.error("Failed to process and save data")
                sys.exit(1)
        else:
            logger.error("No accommodations found")
            sys.exit(1)
        
        # Clean up
        scraper.close()
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

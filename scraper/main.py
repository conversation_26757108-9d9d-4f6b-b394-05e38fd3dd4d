#!/usr/bin/env python3
"""
South African Student Accommodation Scraper
Simple, working scraper for Student24.co
"""

import argparse
import logging
import sys
import os
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import working scrapers
from scrapers.student24_comprehensive_scraper import Student24ComprehensiveScraper, SA_PROVINCES, SA_UNIVERSITIES
from scrapers.student24_working_scraper import Student24WorkingScraper

# Import utilities
from utils.data_processor import DataProcessor


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration"""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Setup logging
    log_file = log_dir / f"scraper_{time.strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized. Log file: {log_file}")


class StudentAccommodationScraper:
    """Main scraper class for student accommodations"""

    def __init__(self, delay: float = 3, max_retries: int = 3, scraper_type: str = "search"):
        self.delay = delay
        self.max_retries = max_retries
        self.scraper_type = scraper_type
        self.logger = logging.getLogger(__name__)

        # Initialize scraper based on type
        if scraper_type == "search":
            self.scraper = Student24SearchScraper(delay=delay, max_retries=max_retries)
        else:
            self.scraper = Student24SimpleScraper(delay=delay, max_retries=max_retries)
    
    def scrape_accommodations(self) -> List[Dict[str, Any]]:
        """Scrape student accommodations"""

        self.logger.info(f"Starting student accommodation scraping using {self.scraper_type} scraper")

        try:
            # Scrape accommodations
            listings = self.scraper.scrape_accommodations()

            self.logger.info(f"Successfully scraped {len(listings)} accommodations")
            return listings

        except Exception as e:
            self.logger.error(f"Error during scraping: {e}")
            return []

        finally:
            # Clean up
            self.scraper.close()
    
    def process_and_save_data(self, listings: List[Dict[str, Any]]) -> Dict[str, str]:
        """Process and save the scraped data"""
        
        if not listings:
            self.logger.warning("No listings to process")
            return {}
        
        self.logger.info(f"Processing {len(listings)} listings...")
        
        # Create output directory
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # Generate timestamp for files
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        
        # Validate and deduplicate listings
        valid_listings = []
        for listing in listings:
            if DataProcessor.validate_listing(listing):
                valid_listings.append(listing)
            else:
                self.logger.warning(f"Invalid listing: {listing.get('title', 'Unknown')}")
        
        if not valid_listings:
            self.logger.error("No valid listings found")
            return {}
        
        # Deduplicate
        deduplicated_listings = DataProcessor.deduplicate_listings(valid_listings)
        self.logger.info(f"Deduplication: {len(valid_listings)} -> {len(deduplicated_listings)} listings")
        
        # Save JSON
        json_file = output_dir / f"student_accommodations_{timestamp}.json"
        DataProcessor.export_to_json(deduplicated_listings, json_file)
        
        # Save CSV
        csv_file = output_dir / f"student_accommodations_{timestamp}.csv"
        DataProcessor.export_to_csv(deduplicated_listings, csv_file)
        
        # Generate report
        report = DataProcessor.generate_report(deduplicated_listings)
        report_file = output_dir / f"report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Files saved:")
        self.logger.info(f"  JSON: {json_file}")
        self.logger.info(f"  CSV: {csv_file}")
        self.logger.info(f"  Report: {report_file}")
        
        return {
            'json': str(json_file),
            'csv': str(csv_file),
            'report': str(report_file)
        }


def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(description='South African Student Accommodation Scraper')
    parser.add_argument('--delay', type=float, default=3.0,
                       help='Delay between requests in seconds (default: 3.0)')
    parser.add_argument('--max-retries', type=int, default=3,
                       help='Maximum number of retries for failed requests (default: 3)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--scraper', choices=['simple', 'search'], default='search',
                       help='Scraper type: simple (direct URLs) or search (university search) (default: search)')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting South African Student Accommodation Scraper")
    logger.info(f"Scraper type: {args.scraper}")
    logger.info(f"Delay: {args.delay}s")
    logger.info(f"Max retries: {args.max_retries}")
    
    try:
        # Initialize scraper
        scraper = StudentAccommodationScraper(
            delay=args.delay,
            max_retries=args.max_retries,
            scraper_type=args.scraper
        )
        
        # Scrape accommodations
        logger.info("=" * 60)
        logger.info("SCRAPING STUDENT ACCOMMODATIONS")
        logger.info("=" * 60)
        
        listings = scraper.scrape_accommodations()
        
        if listings:
            # Process and save data
            files = scraper.process_and_save_data(listings)
            
            # Print summary
            print("\n" + "=" * 60)
            print("SCRAPING SUMMARY")
            print("=" * 60)
            print(f"Total accommodations scraped: {len(listings)}")
            
            # Generate and display report
            report = DataProcessor.generate_report(listings)
            print(f"Sources: {report.get('sources', {})}")
            print("\nData completeness:")
            for key, value in report.get('data_completeness', {}).items():
                print(f"  {key}: {value}")
            
            if files:
                print(f"\nFiles saved:")
                for file_type, file_path in files.items():
                    print(f"  {file_type.upper()}: {file_path}")
            
            print("=" * 60)
            logger.info("Scraping completed successfully!")
            
        else:
            logger.error("No accommodations found")
            print("\n❌ No accommodations found")
            print("Check the logs for more details")
            return 1
    
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        print("\n⚠️ Scraping interrupted by user")
        return 1
    
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ Error: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)

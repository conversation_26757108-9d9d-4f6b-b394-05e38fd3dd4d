#!/usr/bin/env python3
"""
Main scraping script for South African property listings
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dotenv import load_dotenv
except ImportError:
    def load_dotenv():
        pass

try:
    from tqdm import tqdm
except ImportError:
    # Simple fallback if tqdm not available
    def tqdm(iterable, **kwargs):
        return iterable

# Import scrapers
from scrapers.gumtree_scraper import GumtreeScraper, GUMTREE_URLS
from scrapers.property24_scraper import Property24Scraper, PROPERTY24_URLS
from scrapers.privateproperty_scraper import PrivatePropertyScraper, PRIVATEPROPERTY_URLS
from scrapers.student_com_scraper import StudentComScraper, STUDENT_COM_URLS
from scrapers.campus_stay_scraper import CampusStayScraper, CAMPUS_STAY_URLS
from scrapers.student24_scraper import Student24Scraper, STUDENT24_URLS
from utils.data_processor import DataProcessor
from utils.geocoding import GeocodingService

# Load environment variables
load_dotenv()

# Setup logging
def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Create logs directory if it doesn't exist
    Path("logs").mkdir(exist_ok=True)
    
    # File handler
    file_handler = logging.FileHandler(f"logs/scraper_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger

logger = logging.getLogger(__name__)


class PropertyScrapeManager:
    """Main manager for coordinating property scraping across multiple sites"""
    
    def __init__(self, delay: float = 3, max_retries: int = 3):
        self.delay = delay
        self.max_retries = max_retries
        self.scrapers = {
            'gumtree': GumtreeScraper(delay=delay, max_retries=max_retries),
            'property24': Property24Scraper(delay=delay, max_retries=max_retries),
            'privateproperty': PrivatePropertyScraper(delay=delay, max_retries=max_retries),
            'student_com': StudentComScraper(delay=delay, max_retries=max_retries),
            'campus_stay': CampusStayScraper(delay=delay, max_retries=max_retries),
            'student24': Student24Scraper(delay=delay, max_retries=max_retries)
        }
        self.all_listings = []
        self.geocoding_service = GeocodingService()
    
    def scrape_site(self, site_name: str, max_pages: int = 5) -> List[Dict[str, Any]]:
        """Scrape a specific site"""
        if site_name not in self.scrapers:
            logger.error(f"Unknown site: {site_name}")
            return []
        
        scraper = self.scrapers[site_name]
        
        # Get URLs for the site
        if site_name == 'gumtree':
            urls = GUMTREE_URLS
        elif site_name == 'property24':
            urls = PROPERTY24_URLS
        elif site_name == 'privateproperty':
            urls = PRIVATEPROPERTY_URLS
        elif site_name == 'student_com':
            urls = STUDENT_COM_URLS
        elif site_name == 'campus_stay':
            urls = CAMPUS_STAY_URLS
        elif site_name == 'student24':
            urls = STUDENT24_URLS
        else:
            logger.error(f"No URLs defined for site: {site_name}")
            return []
        
        logger.info(f"Starting to scrape {site_name} with {len(urls)} base URLs")
        
        try:
            listings = scraper.scrape_all(urls, max_pages)
            logger.info(f"Successfully scraped {len(listings)} listings from {site_name}")
            return listings
        
        except Exception as e:
            logger.error(f"Error scraping {site_name}: {e}")
            return []
    
    def scrape_all_sites(self, sites: List[str] = None, max_pages: int = 5) -> List[Dict[str, Any]]:
        """Scrape all specified sites"""
        if sites is None:
            sites = list(self.scrapers.keys())
        
        all_listings = []
        
        for site in sites:
            logger.info(f"\n{'='*50}")
            logger.info(f"SCRAPING: {site.upper()}")
            logger.info(f"{'='*50}")
            
            site_listings = self.scrape_site(site, max_pages)
            all_listings.extend(site_listings)
            
            logger.info(f"Completed {site}: {len(site_listings)} listings")
            
            # Progress update
            total_so_far = len(all_listings)
            logger.info(f"Total listings so far: {total_so_far}")
        
        self.all_listings = all_listings
        return all_listings
    
    def process_and_save(self, output_dir: str = "output", deduplicate: bool = True) -> Dict[str, Any]:
        """Process scraped data and save to files"""
        if not self.all_listings:
            logger.warning("No listings to process")
            return {}
        
        logger.info(f"Processing {len(self.all_listings)} listings...")
        
        # Create output directory
        Path(output_dir).mkdir(exist_ok=True)
        
        # Deduplicate if requested
        if deduplicate:
            original_count = len(self.all_listings)
            self.all_listings = DataProcessor.deduplicate_listings(self.all_listings)
            logger.info(f"Deduplication: {original_count} -> {len(self.all_listings)} listings")
        
        # Generate timestamp for files
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save as JSON
        json_file = f"{output_dir}/listings_{timestamp}.json"
        DataProcessor.export_to_json(self.all_listings, json_file)
        
        # Save as CSV
        csv_file = f"{output_dir}/listings_{timestamp}.csv"
        DataProcessor.export_to_csv(self.all_listings, csv_file)
        
        # Generate report
        report = DataProcessor.generate_report(self.all_listings)
        
        # Save report
        report_file = f"{output_dir}/report_{timestamp}.json"
        with open(report_file, 'w') as f:
            import json
            json.dump(report, f, indent=2)
        
        logger.info(f"Files saved:")
        logger.info(f"  JSON: {json_file}")
        logger.info(f"  CSV: {csv_file}")
        logger.info(f"  Report: {report_file}")
        
        return {
            'json_file': json_file,
            'csv_file': csv_file,
            'report_file': report_file,
            'report': report
        }
    
    def print_summary(self, report: Dict[str, Any]):
        """Print a summary of scraping results"""
        print(f"\n{'='*60}")
        print("SCRAPING SUMMARY")
        print(f"{'='*60}")
        
        print(f"Total listings scraped: {report['total_listings']}")
        print(f"Sources: {report['sources']}")
        
        print(f"\nData completeness:")
        for key, value in report['data_completeness'].items():
            print(f"  {key}: {value}")
        
        if report.get('price_statistics'):
            print(f"\nPrice statistics (ZAR):")
            stats = report['price_statistics']
            print(f"  Min: R{stats['min']:,.0f}")
            print(f"  Max: R{stats['max']:,.0f}")
            print(f"  Average: R{stats['average']:,.0f}")
            print(f"  Median: R{stats['median']:,.0f}")
        
        print(f"\n{'='*60}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='South African Property Listing Scraper')
    parser.add_argument('--sites', nargs='+',
                       choices=['gumtree', 'property24', 'privateproperty', 'student_com', 'campus_stay', 'student24'],
                       default=['student24'],
                       help='Sites to scrape (default: student-focused sites)')
    parser.add_argument('--max-pages', type=int, default=5,
                       help='Maximum pages to scrape per site (default: 5)')
    parser.add_argument('--delay', type=float, default=3.0,
                       help='Delay between requests in seconds (default: 3.0)')
    parser.add_argument('--max-retries', type=int, default=3,
                       help='Maximum retries for failed requests (default: 3)')
    parser.add_argument('--output-dir', default='output',
                       help='Output directory (default: output)')
    parser.add_argument('--no-deduplicate', action='store_true',
                       help='Skip deduplication step')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    logger.info("Starting South African Property Scraper")
    logger.info(f"Sites: {args.sites}")
    logger.info(f"Max pages per site: {args.max_pages}")
    logger.info(f"Delay: {args.delay}s")
    logger.info(f"Max retries: {args.max_retries}")
    
    try:
        # Initialize scraper manager
        manager = PropertyScrapeManager(
            delay=args.delay,
            max_retries=args.max_retries
        )
        
        # Scrape all sites
        listings = manager.scrape_all_sites(args.sites, args.max_pages)
        
        if not listings:
            logger.error("No listings were scraped. Exiting.")
            sys.exit(1)
        
        # Process and save data
        result = manager.process_and_save(
            output_dir=args.output_dir,
            deduplicate=not args.no_deduplicate
        )
        
        # Print summary
        manager.print_summary(result['report'])
        
        logger.info("Scraping completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

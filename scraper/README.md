# South African Property Listing Scraper

A comprehensive Python scraping system for extracting student accommodation listings from major South African property websites and generating structured test data for database seeding.

## 🎯 Features

- **Multi-site scraping**: Gumtree, Property24, and Private Property
- **Student-focused areas**: Hatfield (Pretoria), Rondebosch, Observatory (Cape Town)
- **Intelligent geocoding**: OpenCage API with Google Maps fallback
- **Data validation**: JSON schema validation and deduplication
- **Multiple export formats**: JSON, CSV, and SQLite database
- **Robust error handling**: Rate limiting, retry logic, and respectful scraping
- **Comprehensive reporting**: Data quality metrics and statistics

## 📁 Project Structure

```
scraper/
├── __init__.py
├── requirements.txt
├── .env.example
├── main.py                 # Main scraping script
├── import_seed.py          # Data import/export CLI
├── scrapers/
│   ├── __init__.py
│   ├── base_scraper.py     # Base scraper class
│   ├── gumtree_scraper.py
│   ├── property24_scraper.py
│   └── privateproperty_scraper.py
├── utils/
│   ├── __init__.py
│   ├── geocoding.py        # Geocoding service
│   └── data_processor.py   # Data processing utilities
├── output/                 # Generated data files
└── logs/                   # Scraping logs
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project
cd scraper

# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.example .env
```

### 2. API Key Setup

Edit `.env` file with your API keys:

```bash
# Get free API key from OpenCage: https://opencagedata.com/
OPENCAGE_API_KEY=your_opencage_api_key_here

# Optional: Google Maps API key as fallback
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Scraping configuration
SCRAPING_DELAY=3
MAX_RETRIES=3
OUTPUT_FORMAT=json
```

### 3. Run the Scraper

```bash
# Scrape all sites (recommended)
python main.py

# Scrape specific sites only
python main.py --sites gumtree property24

# Custom configuration
python main.py --max-pages 3 --delay 2 --output-dir my_output
```

## 📊 Data Schema

Each listing follows this JSON structure:

```json
{
  "id": "unique_identifier",
  "source": "gumtree|property24|privateproperty",
  "title": "2 Bed Student Flat near UCT",
  "price": 3500,
  "currency": "ZAR",
  "location": {
    "name": "Rondebosch",
    "address": "123 Main Road, Rondebosch, Cape Town",
    "latitude": -33.9577,
    "longitude": 18.4739
  },
  "description": "Full property description...",
  "features": ["2 bedrooms", "1 bathroom", "parking"],
  "images": ["url1", "url2"],
  "contact": {
    "type": "whatsapp|phone|email",
    "value": "https://wa.me/27821234567"
  },
  "scraped_at": "2025-01-17T10:30:00Z",
  "url": "original_listing_url"
}
```

## 🛠️ Usage Examples

### Basic Scraping

```bash
# Scrape all sites with default settings
python main.py

# Scrape only Gumtree with custom pages
python main.py --sites gumtree --max-pages 10

# Faster scraping (less respectful)
python main.py --delay 1 --max-retries 2
```

### Data Import/Export

```bash
# Import JSON to database
python import_seed.py -f output/listings.json -fmt json -t database

# Convert JSON to CSV
python import_seed.py -f output/listings.json -fmt json -t csv -o listings.csv

# Import with validation and deduplication
python import_seed.py -f listings.csv -fmt csv -t database --validate --deduplicate

# Generate data quality report
python import_seed.py -f output/listings.json -fmt json -t json --report
```

## 🎯 Target Websites & Areas

### Gumtree South Africa
- **Hatfield, Pretoria**: Student accommodation near University of Pretoria
- **Rondebosch, Cape Town**: Near University of Cape Town
- **Observatory, Cape Town**: Alternative student area

### Property24
- **Hatfield**: Professional rental listings
- **Rondebosch**: Established rental market
- **Observatory & Mowbray**: Additional Cape Town student areas

### Private Property
- **Hatfield**: Premium rental listings
- **Rondebosch**: High-quality accommodations
- **Observatory & Mowbray**: Diverse rental options

## 📈 Expected Results

- **Minimum 50 listings per website** (150+ total)
- **90%+ successful geocoding rate**
- **Complete data**: title, price, location, contact info
- **Rich metadata**: features, images, descriptions
- **Validated output**: Schema-compliant JSON/CSV

## ⚙️ Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENCAGE_API_KEY` | OpenCage Geocoding API key | Required |
| `GOOGLE_MAPS_API_KEY` | Google Maps API key (fallback) | Optional |
| `SCRAPING_DELAY` | Delay between requests (seconds) | 3 |
| `MAX_RETRIES` | Maximum retry attempts | 3 |
| `OUTPUT_FORMAT` | Default output format | json |
| `LOG_LEVEL` | Logging level | INFO |

### Command Line Options

```bash
python main.py --help

Options:
  --sites [gumtree] [property24] [privateproperty]
                        Sites to scrape (default: all)
  --max-pages INTEGER   Maximum pages to scrape per site (default: 5)
  --delay FLOAT         Delay between requests in seconds (default: 3.0)
  --max-retries INTEGER Maximum retries for failed requests (default: 3)
  --output-dir TEXT     Output directory (default: output)
  --no-deduplicate      Skip deduplication step
  --log-level [DEBUG|INFO|WARNING|ERROR]
                        Logging level (default: INFO)
```

## 🔧 API Key Setup

### OpenCage Geocoding API (Primary)

1. Visit [OpenCage Data](https://opencagedata.com/)
2. Sign up for a free account
3. Get your API key (2,500 requests/day free)
4. Add to `.env`: `OPENCAGE_API_KEY=your_key_here`

### Google Maps Geocoding API (Fallback)

1. Visit [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Geocoding API
3. Create credentials (API key)
4. Add to `.env`: `GOOGLE_MAPS_API_KEY=your_key_here`

## 🚨 Error Handling

The scraper includes comprehensive error handling:

- **Rate limiting**: Respectful delays between requests
- **Retry logic**: Automatic retries for failed requests
- **User agent rotation**: Prevents blocking
- **Graceful degradation**: Continues on individual failures
- **Detailed logging**: All activities logged to files

## 📋 Data Quality Features

- **Schema validation**: Ensures data consistency
- **Deduplication**: Removes duplicate listings
- **Geocoding validation**: Verifies location accuracy
- **Contact extraction**: Intelligent contact info parsing
- **Feature extraction**: Automatic property feature detection

## 🔍 Troubleshooting

### Common Issues

1. **No listings found**
   - Check if website structure has changed
   - Verify internet connection
   - Try reducing `--max-pages` or increasing `--delay`

2. **Geocoding failures**
   - Verify API keys in `.env` file
   - Check API quota limits
   - Fallback coordinates are used when geocoding fails

3. **Rate limiting/blocking**
   - Increase `--delay` parameter
   - Reduce `--max-pages`
   - Check if IP is temporarily blocked

4. **Import errors**
   - Validate JSON/CSV format
   - Check file permissions
   - Use `--validate` flag for debugging

### Legal Compliance

- **robots.txt**: Always check and respect robots.txt
- **Rate limiting**: Built-in delays prevent server overload
- **Terms of service**: Review each website's terms before scraping
- **Data usage**: Only use scraped data for legitimate purposes

## 📝 Output Files

### Generated Files

- `listings_YYYYMMDD_HHMMSS.json`: Complete listing data
- `listings_YYYYMMDD_HHMMSS.csv`: Flattened CSV format
- `report_YYYYMMDD_HHMMSS.json`: Data quality report
- `logs/scraper_YYYYMMDD_HHMMSS.log`: Detailed scraping logs

### Database Schema

SQLite database with `listings` table:

```sql
CREATE TABLE listings (
    id TEXT PRIMARY KEY,
    source TEXT NOT NULL,
    title TEXT NOT NULL,
    price REAL,
    currency TEXT DEFAULT 'ZAR',
    location_name TEXT,
    location_address TEXT,
    latitude REAL,
    longitude REAL,
    description TEXT,
    features TEXT,
    images TEXT,
    contact_type TEXT,
    contact_value TEXT,
    scraped_at TEXT,
    url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please respect website terms of service and use responsibly.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in the `logs/` directory
3. Open an issue with detailed error information

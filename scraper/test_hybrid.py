#!/usr/bin/env python3
"""
Test script for Student24 Hybrid scraper (works without Chrome)
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_hybrid_scraper():
    """Test the hybrid Student24 scraper"""
    
    print("🔄 Testing Student24 Hybrid Scraper")
    print("=" * 50)
    
    try:
        from scrapers.student24_hybrid_scraper import Student24HybridScraper, STUDENT24_HYBRID_URLS
        print("✅ Hybrid scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import hybrid scraper: {e}")
        return False
    
    # Test with the working URL you found
    test_url = "https://student24.co/accommodation?ai=qnht100txslt224txuzt127"
    
    try:
        print(f"\n🔍 Testing URL: {test_url}")
        
        # Initialize scraper (will use requests if Chrome not available)
        scraper = Student24HybridScraper(delay=2, prefer_selenium=False)  # Force requests mode
        print("✅ Scraper initialized in requests mode")
        
        # Scrape the accommodation
        listings = scraper.scrape_accommodations([test_url])
        
        print(f"\n📊 Results:")
        print(f"  Total listings found: {len(listings)}")
        
        if listings:
            for i, listing in enumerate(listings):
                print(f"\n  📋 Listing {i+1}:")
                print(f"    Title: {listing.get('title', 'N/A')}")
                print(f"    Price: R{listing.get('price', 0):,.0f}")
                print(f"    Location: {listing.get('location', {}).get('name', 'N/A')}")
                print(f"    Features: {len(listing.get('features', []))} features")
                print(f"    Contact: {listing.get('contact', {}).get('type', 'N/A')}")
                print(f"    Description: {listing.get('description', '')[:100]}...")
                
                # Show coordinates if available
                location = listing.get('location', {})
                if location.get('latitude'):
                    print(f"    Coordinates: {location['latitude']:.4f}, {location['longitude']:.4f}")
            
            # Save results
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            output_file = output_dir / "hybrid_test_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "listings": listings,
                    "metadata": {
                        "total_count": len(listings),
                        "test_url": test_url,
                        "scraper": "student24_hybrid",
                        "method": "requests"
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Results saved to: {output_file}")
            
            # Test data processing
            try:
                from utils.data_processor import DataProcessor
                
                # Validate listings
                valid_count = sum(1 for listing in listings if DataProcessor.validate_listing(listing))
                print(f"\n✅ Validation: {valid_count}/{len(listings)} listings are valid")
                
                # Generate report
                report = DataProcessor.generate_report(listings)
                print(f"\n📈 Data Quality Report:")
                print(f"  Sources: {report.get('sources', {})}")
                for key, value in report.get('data_completeness', {}).items():
                    print(f"  {key}: {value}")
                
                if report.get('price_statistics'):
                    stats = report['price_statistics']
                    print(f"\n💰 Price Statistics:")
                    print(f"  Average: R{stats['average']:,.0f}")
                    print(f"  Range: R{stats['min']:,.0f} - R{stats['max']:,.0f}")
                
            except Exception as e:
                print(f"⚠️  Data processing test failed: {e}")
        
        else:
            print("❌ No listings found")
            print("\n🔧 This could mean:")
            print("1. The URL structure has changed")
            print("2. The website requires JavaScript (try installing Chrome for Selenium)")
            print("3. The website is blocking requests")
        
        return len(listings) > 0
        
    except Exception as e:
        print(f"❌ Error testing hybrid scraper: {e}")
        return False

def test_multiple_urls():
    """Test multiple URLs"""
    
    print("\n🔍 Testing Multiple Student24 URLs")
    print("=" * 50)
    
    try:
        from scrapers.student24_hybrid_scraper import Student24HybridScraper, STUDENT24_HYBRID_URLS
        
        scraper = Student24HybridScraper(delay=1, prefer_selenium=False)
        
        all_listings = []
        
        for i, url in enumerate(STUDENT24_HYBRID_URLS[:3]):  # Test first 3 URLs
            print(f"\n🌐 Testing URL {i+1}: {url}")
            
            try:
                listings = scraper.scrape_accommodations([url])
                print(f"  Result: {len(listings)} listings found")
                all_listings.extend(listings)
                
                if listings:
                    print(f"  ✅ Success! Found: {listings[0].get('title', 'Unknown')}")
                else:
                    print(f"  ⚠️  No listings found")
                
            except Exception as e:
                print(f"  ❌ Failed: {e}")
        
        print(f"\n📊 Total Results: {len(all_listings)} listings from all URLs")
        
        if all_listings:
            # Save combined results
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            combined_file = output_dir / "student24_combined_results.json"
            with open(combined_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "listings": all_listings,
                    "metadata": {
                        "total_count": len(all_listings),
                        "urls_tested": STUDENT24_HYBRID_URLS[:3],
                        "scraper": "student24_hybrid"
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Combined results saved to: {combined_file}")
        
        return len(all_listings) > 0
        
    except Exception as e:
        print(f"❌ Error in multiple URL test: {e}")
        return False

def main():
    """Run hybrid scraper tests"""
    
    print("🧪 Student24 Hybrid Scraper Test Suite")
    print("=" * 60)
    print("This scraper works without Chrome/Selenium installation")
    print()
    
    # Test 1: Single URL test
    success1 = test_hybrid_scraper()
    
    # Test 2: Multiple URLs test
    success2 = test_multiple_urls()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    if success1 or success2:
        print("🎉 SUCCESS! Hybrid scraper is working")
        print("\nWhat we found:")
        print("✅ Can connect to Student24.co")
        print("✅ Can extract accommodation data")
        print("✅ Data validation works")
        print("✅ Geocoding works with fallback coordinates")
        
        print("\nNext steps:")
        print("1. Integrate with main scraper")
        print("2. Scale up to scrape more accommodations")
        print("3. Install Chrome for better dynamic content support")
        
        print("\nTo use in main scraper:")
        print("python3 main.py --sites student24_hybrid --max-pages 2")
        
    else:
        print("❌ Tests failed. The website might have changed or be blocking requests.")
        print("\nTroubleshooting:")
        print("1. Check internet connectivity")
        print("2. Try installing Chrome for Selenium support")
        print("3. The website structure might have changed")
    
    return success1 or success2

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test the simple Student24 scraper and show detailed accommodation data
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_scraper():
    """Test the simple scraper and show detailed results"""
    
    print("🏠 Testing Simple Student24 Scraper")
    print("=" * 60)
    print("This scraper only uses working functionality (no Selenium)")
    print()
    
    try:
        from scrapers.student24_simple import Student24SimpleScraper
        print("✅ Simple scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import simple scraper: {e}")
        return False
    
    try:
        # Create output directory
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # Initialize scraper
        print("🚀 Initializing scraper...")
        scraper = Student24SimpleScraper(delay=2)
        
        # Scrape accommodations
        print("📡 Scraping accommodations...")
        listings = scraper.scrape_accommodations()
        
        print(f"\n📊 SCRAPING RESULTS:")
        print(f"Total accommodations found: {len(listings)}")
        
        if listings:
            print(f"\n🏠 ACCOMMODATION DETAILS:")
            print("=" * 60)
            
            for i, listing in enumerate(listings, 1):
                print(f"\n📋 ACCOMMODATION {i}:")
                print(f"  🏷️  Title: {listing.get('title', 'N/A')}")
                print(f"  💰 Price: R{listing.get('price', 0):,.0f} per month")
                print(f"  📍 Location: {listing.get('location', {}).get('name', 'N/A')}")
                
                # Show coordinates if available
                location = listing.get('location', {})
                if location.get('latitude'):
                    print(f"  🗺️  Coordinates: {location['latitude']:.4f}, {location['longitude']:.4f}")
                
                print(f"  📝 Description: {listing.get('description', 'N/A')[:100]}...")
                print(f"  🏷️  Features: {', '.join(listing.get('features', [])[:5])}")
                print(f"  📞 Contact: {listing.get('contact', {}).get('type', 'N/A')} - {listing.get('contact', {}).get('value', 'N/A')}")
                print(f"  🔗 URL: {listing.get('url', 'N/A')}")
                
                if listing.get('images'):
                    print(f"  🖼️  Images: {len(listing.get('images', []))} images available")
                
                print(f"  🕒 Scraped: {listing.get('scraped_at', 'N/A')}")
            
            # Save detailed results
            detailed_file = output_dir / "student24_simple_detailed.json"
            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "accommodations": listings,
                    "summary": {
                        "total_count": len(listings),
                        "scraper": "student24_simple",
                        "universities_covered": [
                            "University of Johannesburg",
                            "University of Cape Town", 
                            "University of the Witwatersrand",
                            "University of Pretoria",
                            "Stellenbosch University"
                        ]
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Detailed results saved to: {detailed_file}")
            
            # Test data validation
            print(f"\n🔍 DATA VALIDATION:")
            try:
                from utils.data_processor import DataProcessor
                
                # Validate each listing
                valid_listings = []
                for listing in listings:
                    if DataProcessor.validate_listing(listing):
                        valid_listings.append(listing)
                        print(f"  ✅ {listing['title']}: Valid")
                    else:
                        print(f"  ❌ {listing['title']}: Invalid")
                
                print(f"\n📈 VALIDATION SUMMARY:")
                print(f"  Valid listings: {len(valid_listings)}/{len(listings)}")
                
                if valid_listings:
                    # Generate data quality report
                    report = DataProcessor.generate_report(valid_listings)
                    
                    print(f"\n📊 DATA QUALITY REPORT:")
                    print(f"  Sources: {report.get('sources', {})}")
                    print(f"  Data completeness:")
                    for key, value in report.get('data_completeness', {}).items():
                        print(f"    {key}: {value}")
                    
                    if report.get('price_statistics'):
                        stats = report['price_statistics']
                        print(f"  Price statistics:")
                        print(f"    Average: R{stats['average']:,.0f}")
                        print(f"    Range: R{stats['min']:,.0f} - R{stats['max']:,.0f}")
                
            except Exception as e:
                print(f"⚠️  Data validation error: {e}")
            
            # Clean up
            scraper.close()
            
            return True
        
        else:
            print("❌ No accommodations found")
            print("\nPossible reasons:")
            print("1. Website structure has changed")
            print("2. Network connectivity issues")
            print("3. Website is blocking requests")
            
            scraper.close()
            return False
        
    except Exception as e:
        print(f"❌ Error testing simple scraper: {e}")
        return False

def show_json_structure():
    """Show the JSON structure of scraped data"""
    
    print("\n📄 JSON OUTPUT STRUCTURE:")
    print("=" * 60)
    
    sample_structure = {
        "accommodations": [
            {
                "id": "unique_12_char_id",
                "source": "student24_simple",
                "title": "The Richmond",
                "price": 4500,
                "currency": "ZAR",
                "location": {
                    "name": "Johannesburg",
                    "latitude": -26.1849,
                    "longitude": 28.0077
                },
                "description": "Student accommodation near University of Johannesburg...",
                "features": ["student accommodation", "university area", "wifi", "security"],
                "images": ["https://student24.co/image1.jpg"],
                "contact": {
                    "type": "website",
                    "value": "https://student24.co/accommodation?ai=..."
                },
                "scraped_at": "2025-07-18T15:30:00Z",
                "url": "https://student24.co/accommodation?ai=..."
            }
        ],
        "summary": {
            "total_count": 5,
            "scraper": "student24_simple",
            "universities_covered": ["UJ", "UCT", "Wits", "UP", "Stellenbosch"]
        }
    }
    
    print(json.dumps(sample_structure, indent=2))

def main():
    """Run the simple scraper test"""
    
    print("🧪 Student24 Simple Scraper Test")
    print("=" * 60)
    
    # Show expected JSON structure
    show_json_structure()
    
    # Run the test
    success = test_simple_scraper()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS! Simple scraper is working")
        print("\nWhat we confirmed:")
        print("✅ Can scrape real accommodation data")
        print("✅ Extracts titles, prices, locations, features")
        print("✅ Provides proper geocoding coordinates")
        print("✅ Generates valid JSON output")
        print("✅ Works without external dependencies")
        
        print("\nJSON output contains:")
        print("📄 Structured accommodation data")
        print("📍 University locations with coordinates")
        print("💰 Pricing information (where available)")
        print("🏷️  Property features and descriptions")
        print("📞 Contact information")
        
        print("\nReady for:")
        print("🔄 Integration with your application")
        print("💾 Database import")
        print("📊 Data analysis")
        
    else:
        print("❌ Test failed")
        print("\nTroubleshooting:")
        print("1. Check internet connectivity")
        print("2. Verify Student24.co is accessible")
        print("3. Check if website structure changed")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Debug tool to examine Student24.co page content and find the correct selectors
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_student24_page(url):
    """Debug a specific Student24 page to understand its structure"""
    
    print(f"🔍 Debugging Student24 page: {url}")
    print("=" * 80)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch page: {response.status_code}")
            return
        
        print(f"✅ Successfully fetched page ({len(response.content)} bytes)")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Page title
        title = soup.title.string if soup.title else "No title"
        print(f"📄 Page title: {title}")
        
        # Look for accommodation-related content
        print("\n🏠 Accommodation Content Analysis:")
        
        # Check for accommodation keywords
        page_text = soup.get_text().lower()
        accommodation_keywords = [
            'accommodation', 'residence', 'student', 'room', 'apartment', 
            'housing', 'property', 'rental', 'booking', 'available'
        ]
        
        found_keywords = []
        for keyword in accommodation_keywords:
            count = page_text.count(keyword)
            if count > 0:
                found_keywords.append(f"{keyword}({count})")
        
        if found_keywords:
            print(f"  ✅ Keywords found: {', '.join(found_keywords)}")
        else:
            print("  ❌ No accommodation keywords found")
        
        # Look for potential listing containers
        print("\n📦 Potential Listing Containers:")
        
        container_selectors = [
            'div[class*="card"]',
            'div[class*="item"]', 
            'div[class*="listing"]',
            'div[class*="property"]',
            'div[class*="accommodation"]',
            'div[class*="residence"]',
            'article',
            '.result',
            '.property-card',
            '.accommodation-item'
        ]
        
        total_containers = 0
        for selector in container_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  📦 {selector}: {len(elements)} elements")
                total_containers += len(elements)
                
                # Show first element content
                if elements:
                    first_elem = elements[0]
                    text_preview = first_elem.get_text()[:100].replace('\n', ' ').strip()
                    print(f"      Preview: {text_preview}...")
        
        if total_containers == 0:
            print("  ❌ No obvious listing containers found")
        
        # Look for links that might be accommodation listings
        print("\n🔗 Potential Accommodation Links:")
        
        all_links = soup.find_all('a', href=True)
        accommodation_links = []
        
        for link in all_links:
            href = link.get('href')
            text = link.get_text().strip()
            
            # Check if link or text contains accommodation-related keywords
            if any(keyword in href.lower() or keyword in text.lower() for keyword in 
                  ['accommodation', 'residence', 'property', 'room', 'apartment']):
                full_url = urljoin(url, href)
                accommodation_links.append((text[:50], full_url))
        
        if accommodation_links:
            print(f"  ✅ Found {len(accommodation_links)} potential accommodation links:")
            for text, link_url in accommodation_links[:10]:  # Show first 10
                print(f"      📎 {text}: {link_url}")
        else:
            print("  ❌ No accommodation links found")
        
        # Look for images that might be property photos
        print("\n🖼️ Images Analysis:")
        
        images = soup.find_all('img')
        property_images = []
        
        for img in images:
            src = img.get('src', '')
            alt = img.get('alt', '')
            
            if any(keyword in src.lower() or keyword in alt.lower() for keyword in 
                  ['property', 'accommodation', 'residence', 'room', 'apartment']):
                property_images.append(src)
        
        if property_images:
            print(f"  ✅ Found {len(property_images)} potential property images")
        else:
            print("  ❌ No obvious property images found")
        
        # Look for price information
        print("\n💰 Price Information:")
        
        price_patterns = [
            r'R\s*\d+(?:,\d{3})*',
            r'\d+(?:,\d{3})*\s*per\s*month',
            r'Price[:\s]*R?\s*\d+',
            r'\d+(?:,\d{3})*\s*ZAR'
        ]
        
        found_prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            found_prices.extend(matches)
        
        if found_prices:
            print(f"  ✅ Found potential prices: {', '.join(found_prices[:5])}")
        else:
            print("  ❌ No price information found")
        
        # Check for JavaScript/dynamic content
        print("\n⚙️ Dynamic Content Analysis:")
        
        scripts = soup.find_all('script')
        js_indicators = []
        
        for script in scripts:
            script_content = script.get_text().lower()
            
            # Look for common JS frameworks/libraries
            if 'react' in script_content:
                js_indicators.append('React')
            if 'vue' in script_content:
                js_indicators.append('Vue')
            if 'angular' in script_content:
                js_indicators.append('Angular')
            if 'jquery' in script_content:
                js_indicators.append('jQuery')
            if 'ajax' in script_content:
                js_indicators.append('AJAX')
            if 'fetch(' in script_content:
                js_indicators.append('Fetch API')
        
        if js_indicators:
            print(f"  ⚠️  JavaScript frameworks detected: {', '.join(set(js_indicators))}")
            print("      This site might load content dynamically!")
        else:
            print("  ✅ No obvious JavaScript frameworks detected")
        
        # Look for data attributes that might indicate dynamic loading
        dynamic_elements = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        if dynamic_elements:
            print(f"  ⚠️  Found {len(dynamic_elements)} elements with data attributes")
        
        # Summary and recommendations
        print("\n" + "=" * 80)
        print("📋 SUMMARY & RECOMMENDATIONS")
        print("=" * 80)
        
        if found_keywords:
            print("✅ Page contains accommodation-related content")
        else:
            print("❌ Page doesn't seem to contain accommodation content")
        
        if accommodation_links:
            print("✅ Found links to potential accommodation listings")
        else:
            print("❌ No accommodation listing links found")
        
        if js_indicators:
            print("⚠️  Site uses JavaScript - content might be loaded dynamically")
            print("   Consider using Selenium for dynamic content")
        
        if total_containers == 0 and not accommodation_links:
            print("❌ No obvious listing structure found")
            print("   This might be a search page or the content is loaded via JavaScript")
        
        print("\n🎯 Next Steps:")
        if accommodation_links:
            print("1. Try scraping the individual accommodation links found")
            print("2. Update CSS selectors based on the container analysis")
        
        if js_indicators:
            print("3. Consider using Selenium WebDriver for JavaScript content")
        
        print("4. Check if this is a search results page that needs form submission")
        
        # Save page content for manual inspection
        debug_file = f"debug_page_{url.split('/')[-1].replace('?', '_').replace('=', '_')}.html"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(str(soup.prettify()))
        print(f"\n💾 Page content saved to: {debug_file}")
        
    except Exception as e:
        print(f"❌ Error debugging page: {e}")

def main():
    """Debug the Student24 accommodation page"""
    
    # The URL you found that works
    url = "https://student24.co/accommodation?ai=qnht100txslt224txuzt127"
    
    debug_student24_page(url)

if __name__ == '__main__':
    main()

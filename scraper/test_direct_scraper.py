#!/usr/bin/env python3
"""
Test the direct Student24 scraper using accommodation URLs from browser analysis
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_direct_scraper():
    """Test the direct scraper using accommodation URLs"""
    
    print("🎯 Testing DIRECT Student24 Scraper")
    print("=" * 70)
    print("This scraper uses direct accommodation URLs from browser analysis")
    print()
    
    try:
        from scrapers.student24_direct_scraper import Student24DirectScraper, DIRECT_ACCOMMODATION_URLS
        print("✅ Direct scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import direct scraper: {e}")
        return False
    
    try:
        # Create output directory
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # Initialize scraper
        print("🚀 Initializing direct scraper...")
        scraper = Student24DirectScraper(delay=2)
        
        # Show what URLs we'll scrape
        print(f"\n🎯 Will scrape {len(DIRECT_ACCOMMODATION_URLS)} direct accommodation URLs:")
        for i, url in enumerate(DIRECT_ACCOMMODATION_URLS, 1):
            print(f"  {i}. {url}")
        
        # Scrape accommodations
        print(f"\n📡 Starting direct scraping from individual accommodation pages...")
        
        listings = scraper.scrape_accommodations()
        
        print(f"\n📊 DIRECT SCRAPING RESULTS:")
        print(f"Total accommodations found: {len(listings)}")
        
        if listings:
            print(f"\n🏠 DIRECT ACCOMMODATION DETAILS:")
            print("=" * 70)
            
            for i, listing in enumerate(listings, 1):
                print(f"\n📋 ACCOMMODATION {i}:")
                print(f"  🏷️  Title: {listing.get('title', 'N/A')}")
                print(f"  💰 Price: R{listing.get('price', 0):,.0f} per month")
                print(f"  📍 Location: {listing.get('location', {}).get('name', 'N/A')}")
                
                # Show coordinates if available
                location = listing.get('location', {})
                if location.get('latitude'):
                    print(f"  🗺️  Coordinates: {location['latitude']:.4f}, {location['longitude']:.4f}")
                
                description = listing.get('description', '')
                if description:
                    print(f"  📝 Description: {description[:100]}...")
                
                features = listing.get('features', [])
                if features:
                    print(f"  🏷️  Features: {', '.join(features[:5])}")
                
                contact = listing.get('contact', {})
                print(f"  📞 Contact: {contact.get('type', 'N/A')}")
                
                if listing.get('images'):
                    print(f"  🖼️  Images: {len(listing.get('images', []))} images available")
                    print(f"  🖼️  First image: {listing.get('images', ['N/A'])[0][:50]}...")
                
                print(f"  🔗 URL: {listing.get('url', 'N/A')}")
                print(f"  🕒 Scraped: {listing.get('scraped_at', 'N/A')}")
            
            # Save detailed results
            detailed_file = output_dir / "student24_direct_detailed.json"
            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "accommodations": listings,
                    "metadata": {
                        "total_count": len(listings),
                        "scraper": "student24_direct",
                        "method": "direct_accommodation_urls",
                        "urls_scraped": DIRECT_ACCOMMODATION_URLS,
                        "source": "browser_html_analysis"
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Detailed results saved to: {detailed_file}")
            
            # Test data validation
            print(f"\n🔍 DATA VALIDATION:")
            try:
                from utils.data_processor import DataProcessor
                
                # Validate each listing
                valid_listings = []
                for listing in listings:
                    if DataProcessor.validate_listing(listing):
                        valid_listings.append(listing)
                        print(f"  ✅ {listing['title']}: Valid")
                    else:
                        print(f"  ❌ {listing['title']}: Invalid")
                
                print(f"\n📈 VALIDATION SUMMARY:")
                print(f"  Valid listings: {len(valid_listings)}/{len(listings)}")
                
                if valid_listings:
                    # Generate data quality report
                    report = DataProcessor.generate_report(valid_listings)
                    
                    print(f"\n📊 DATA QUALITY REPORT:")
                    print(f"  Sources: {report.get('sources', {})}")
                    print(f"  Data completeness:")
                    for key, value in report.get('data_completeness', {}).items():
                        print(f"    {key}: {value}")
                    
                    if report.get('price_statistics'):
                        stats = report['price_statistics']
                        print(f"  Price statistics:")
                        print(f"    Average: R{stats['average']:,.0f}")
                        print(f"    Range: R{stats['min']:,.0f} - R{stats['max']:,.0f}")
                    
                    # Show accommodation breakdown by university
                    university_breakdown = {}
                    for listing in valid_listings:
                        location = listing.get('location', {}).get('name', 'Unknown')
                        university_breakdown[location] = university_breakdown.get(location, 0) + 1
                    
                    print(f"\n🏫 University Breakdown:")
                    for location, count in university_breakdown.items():
                        print(f"    {location}: {count} accommodations")
                    
                    # Compare with expected accommodations from browser
                    expected_accommodations = [
                        "The Richmond", "Richmond Central", "Kingsway Place", 
                        "Horizon Heights", "Festivals Edge", "Studios Burnett",
                        "Peak Studios", "Varsity Studios"
                    ]
                    
                    found_titles = [listing['title'] for listing in valid_listings]
                    
                    print(f"\n🎯 EXPECTED vs FOUND:")
                    for expected in expected_accommodations:
                        found = any(expected.lower() in title.lower() for title in found_titles)
                        status = "✅" if found else "❌"
                        print(f"    {status} {expected}")
                    
                    # Save validated results
                    validated_file = output_dir / "student24_direct_validated.json"
                    with open(validated_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            "accommodations": valid_listings,
                            "report": report,
                            "university_breakdown": university_breakdown,
                            "expected_vs_found": {
                                "expected": expected_accommodations,
                                "found": found_titles,
                                "match_rate": f"{sum(1 for exp in expected_accommodations if any(exp.lower() in title.lower() for title in found_titles))}/{len(expected_accommodations)}"
                            },
                            "metadata": {
                                "total_count": len(valid_listings),
                                "validation_success_rate": f"{len(valid_listings)}/{len(listings)}",
                                "scraper": "student24_direct",
                                "extraction_method": "direct_url_scraping"
                            }
                        }, f, indent=2, ensure_ascii=False)
                    
                    print(f"\n💾 Validated results saved to: {validated_file}")
                
            except Exception as e:
                print(f"⚠️  Data validation error: {e}")
            
            # Clean up
            scraper.close()
            
            return True
        
        else:
            print("❌ No accommodations found")
            print("\nPossible reasons:")
            print("1. Individual accommodation pages have different structure")
            print("2. Accommodation URLs are no longer valid")
            print("3. Network connectivity issues")
            print("4. Website is blocking requests")
            
            print("\n🔧 Troubleshooting suggestions:")
            print("1. Check if individual accommodation URLs are accessible")
            print("2. Verify accommodation page structure")
            print("3. Try with longer delays")
            print("4. Check logs for specific errors")
            
            scraper.close()
            return False
        
    except Exception as e:
        print(f"❌ Error testing direct scraper: {e}")
        print(f"\n🔧 Error details: {type(e).__name__}: {e}")
        return False

def main():
    """Run the direct scraper test"""
    
    print("🧪 Student24 DIRECT Scraper Test")
    print("=" * 70)
    print("Using direct accommodation URLs from browser analysis")
    print()
    
    success = test_direct_scraper()
    
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    if success:
        print("🎉 SUCCESS! Direct scraper is working")
        print("\nWhat we achieved:")
        print("✅ Used direct accommodation URLs from browser")
        print("✅ Scraped individual accommodation pages")
        print("✅ Extracted real accommodation data")
        print("✅ Validated and processed data")
        print("✅ Compared with expected accommodations")
        
        print("\nDirect scraping benefits:")
        print("📄 Bypasses dynamic content loading issues")
        print("💰 Gets real pricing and details")
        print("📍 Accurate university locations")
        print("🏷️  Complete property information")
        print("📞 Direct contact links")
        
        print("\nNext steps:")
        print("1. Use this scraper in production")
        print("2. Add more accommodation URLs as discovered")
        print("3. Integrate with main application")
        
    else:
        print("❌ Test failed")
        print("\nTroubleshooting:")
        print("1. Check if accommodation URLs are still valid")
        print("2. Verify individual page structure")
        print("3. Check network connectivity")
        print("4. Review error logs for details")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script for Student24 Selenium scraper
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_selenium_scraper():
    """Test the Selenium-based Student24 scraper"""
    
    print("🤖 Testing Student24 Selenium Scraper")
    print("=" * 50)
    
    try:
        from scrapers.student24_selenium_scraper import Student24SeleniumScraper, STUDENT24_SELENIUM_URLS
        print("✅ Selenium scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Selenium scraper: {e}")
        print("\n💡 To fix this, install Selenium:")
        print("pip install selenium webdriver-manager")
        return False
    
    # Test with the working URL you found
    test_url = "https://student24.co/accommodation?ai=qnht100txslt224txuzt127"
    
    try:
        print(f"\n🔍 Testing URL: {test_url}")
        
        # Initialize scraper (headless mode for server environments)
        scraper = Student24SeleniumScraper(delay=2, headless=True)
        print("✅ Scraper initialized")
        
        # Scrape the accommodation
        listings = scraper.scrape_accommodations([test_url])
        
        print(f"\n📊 Results:")
        print(f"  Total listings found: {len(listings)}")
        
        if listings:
            for i, listing in enumerate(listings):
                print(f"\n  📋 Listing {i+1}:")
                print(f"    Title: {listing.get('title', 'N/A')}")
                print(f"    Price: R{listing.get('price', 0):,.0f}")
                print(f"    Location: {listing.get('location', {}).get('name', 'N/A')}")
                print(f"    Features: {len(listing.get('features', []))} features")
                print(f"    Images: {len(listing.get('images', []))} images")
                print(f"    Contact: {listing.get('contact', {}).get('type', 'N/A')}")
                print(f"    Description: {listing.get('description', '')[:100]}...")
            
            # Save results
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            output_file = output_dir / "selenium_test_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "listings": listings,
                    "metadata": {
                        "total_count": len(listings),
                        "test_url": test_url,
                        "scraper": "student24_selenium"
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Results saved to: {output_file}")
            
            # Test data processing
            try:
                from utils.data_processor import DataProcessor
                
                # Validate listings
                valid_count = sum(1 for listing in listings if DataProcessor.validate_listing(listing))
                print(f"\n✅ Validation: {valid_count}/{len(listings)} listings are valid")
                
                # Generate report
                report = DataProcessor.generate_report(listings)
                print(f"\n📈 Data Quality Report:")
                print(f"  Sources: {report.get('sources', {})}")
                for key, value in report.get('data_completeness', {}).items():
                    print(f"  {key}: {value}")
                
            except Exception as e:
                print(f"⚠️  Data processing test failed: {e}")
        
        else:
            print("❌ No listings found")
            print("\n🔧 Troubleshooting suggestions:")
            print("1. Check if the URL is still valid")
            print("2. The website might have changed its structure")
            print("3. Try running with headless=False to see what's happening")
            print("4. Check if Chrome/Chromium is installed")
        
        return len(listings) > 0
        
    except Exception as e:
        print(f"❌ Error testing Selenium scraper: {e}")
        print(f"\n🔧 Error details: {type(e).__name__}: {e}")
        
        if "chrome" in str(e).lower():
            print("\n💡 Chrome/Chromium installation issue:")
            print("  - Install Chrome: sudo apt-get install google-chrome-stable")
            print("  - Or install Chromium: sudo apt-get install chromium-browser")
        
        return False

def test_multiple_urls():
    """Test multiple URLs to see which ones work"""
    
    print("\n🔍 Testing Multiple Student24 URLs")
    print("=" * 50)
    
    try:
        from scrapers.student24_selenium_scraper import Student24SeleniumScraper, STUDENT24_SELENIUM_URLS
        
        scraper = Student24SeleniumScraper(delay=1, headless=True)
        
        all_listings = []
        
        for i, url in enumerate(STUDENT24_SELENIUM_URLS[:3]):  # Test first 3 URLs
            print(f"\n🌐 Testing URL {i+1}: {url}")
            
            try:
                listings = scraper.scrape_accommodations([url])
                print(f"  Result: {len(listings)} listings found")
                all_listings.extend(listings)
                
                if listings:
                    print(f"  ✅ Success! Found: {listings[0].get('title', 'Unknown')}")
                
            except Exception as e:
                print(f"  ❌ Failed: {e}")
        
        print(f"\n📊 Total Results: {len(all_listings)} listings from all URLs")
        return len(all_listings) > 0
        
    except Exception as e:
        print(f"❌ Error in multiple URL test: {e}")
        return False

def main():
    """Run Selenium scraper tests"""
    
    print("🧪 Student24 Selenium Scraper Test Suite")
    print("=" * 60)
    
    # Test 1: Single URL test
    success1 = test_selenium_scraper()
    
    # Test 2: Multiple URLs test
    success2 = test_multiple_urls()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    if success1 or success2:
        print("🎉 SUCCESS! Selenium scraper is working")
        print("\nNext steps:")
        print("1. Integrate with main scraper: python3 main.py --sites student24_selenium")
        print("2. Adjust selectors if needed for better data extraction")
        print("3. Scale up to scrape more accommodations")
    else:
        print("❌ Tests failed. Check the troubleshooting suggestions above.")
        print("\nCommon issues:")
        print("1. Chrome/Chromium not installed")
        print("2. Selenium packages not installed")
        print("3. Website structure changed")
        print("4. Network connectivity issues")
    
    return success1 or success2

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Data import and seeding CLI tool for property listings
"""

import os
import json
import csv
import sqlite3
import logging
from pathlib import Path
from typing import List, Dict, Any
import click
import pandas as pd
from utils.data_processor import DataProcessor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseSeeder:
    """Handle database operations for seeding property data"""
    
    def __init__(self, db_path: str = "listings.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create listings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS listings (
                id TEXT PRIMARY KEY,
                source TEXT NOT NULL,
                title TEXT NOT NULL,
                price REAL,
                currency TEXT DEFAULT 'ZAR',
                location_name TEXT,
                location_address TEXT,
                latitude REAL,
                longitude REAL,
                description TEXT,
                features TEXT,
                images TEXT,
                contact_type TEXT,
                contact_value TEXT,
                scraped_at TEXT,
                url TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"Database initialized: {self.db_path}")
    
    def seed_listings(self, listings: List[Dict[str, Any]]) -> int:
        """Insert listings into database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for listing in listings:
            try:
                # Prepare data for insertion
                location = listing.get('location', {})
                contact = listing.get('contact', {})
                
                cursor.execute('''
                    INSERT OR REPLACE INTO listings (
                        id, source, title, price, currency,
                        location_name, location_address, latitude, longitude,
                        description, features, images,
                        contact_type, contact_value, scraped_at, url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    listing.get('id'),
                    listing.get('source'),
                    listing.get('title'),
                    listing.get('price'),
                    listing.get('currency', 'ZAR'),
                    location.get('name'),
                    location.get('address'),
                    location.get('latitude'),
                    location.get('longitude'),
                    listing.get('description'),
                    ', '.join(listing.get('features', [])),
                    ', '.join(listing.get('images', [])),
                    contact.get('type'),
                    contact.get('value'),
                    listing.get('scraped_at'),
                    listing.get('url')
                ))
                
                inserted_count += 1
                
            except Exception as e:
                logger.error(f"Error inserting listing {listing.get('id')}: {e}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"Inserted {inserted_count} listings into database")
        return inserted_count


def load_json_data(file_path: str) -> List[Dict[str, Any]]:
    """Load listings from JSON file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if isinstance(data, dict) and 'listings' in data:
        return data['listings']
    elif isinstance(data, list):
        return data
    else:
        raise ValueError("Invalid JSON format. Expected 'listings' key or array of listings.")


def load_csv_data(file_path: str) -> List[Dict[str, Any]]:
    """Load listings from CSV file and convert to standard format"""
    df = pd.read_csv(file_path)
    listings = []
    
    for _, row in df.iterrows():
        # Convert CSV row back to nested structure
        listing = {
            'id': row.get('id'),
            'source': row.get('source'),
            'title': row.get('title'),
            'price': row.get('price'),
            'currency': row.get('currency', 'ZAR'),
            'location': {
                'name': row.get('location_name'),
                'address': row.get('location_address'),
                'latitude': row.get('latitude') if pd.notna(row.get('latitude')) else None,
                'longitude': row.get('longitude') if pd.notna(row.get('longitude')) else None,
            },
            'description': row.get('description', ''),
            'features': row.get('features', '').split(', ') if row.get('features') else [],
            'images': row.get('images', '').split(', ') if row.get('images') else [],
            'contact': {
                'type': row.get('contact_type'),
                'value': row.get('contact_value')
            } if row.get('contact_type') else None,
            'scraped_at': row.get('scraped_at'),
            'url': row.get('url')
        }
        listings.append(listing)
    
    return listings


@click.command()
@click.option('--file', '-f', required=True, help='Input file path')
@click.option('--format', '-fmt', type=click.Choice(['json', 'csv']), required=True, help='Input file format')
@click.option('--target', '-t', type=click.Choice(['database', 'json', 'csv']), required=True, help='Output target')
@click.option('--output', '-o', help='Output file path (for json/csv targets)')
@click.option('--db-path', default='listings.db', help='Database file path')
@click.option('--validate', is_flag=True, help='Validate data before processing')
@click.option('--deduplicate', is_flag=True, help='Remove duplicate listings')
@click.option('--report', is_flag=True, help='Generate data quality report')
def main(file, format, target, output, db_path, validate, deduplicate, report):
    """
    Import and seed property listing data
    
    Examples:
    
    \b
    # Import JSON to database
    python import_seed.py -f output/listings.json -fmt json -t database
    
    \b
    # Convert JSON to CSV
    python import_seed.py -f output/listings.json -fmt json -t csv -o listings.csv
    
    \b
    # Import CSV to database with validation
    python import_seed.py -f listings.csv -fmt csv -t database --validate --deduplicate
    """
    
    # Validate input file exists
    if not os.path.exists(file):
        click.echo(f"Error: File '{file}' not found.", err=True)
        return
    
    try:
        # Load data based on format
        click.echo(f"Loading data from {file}...")
        
        if format == 'json':
            listings = load_json_data(file)
        elif format == 'csv':
            listings = load_csv_data(file)
        else:
            click.echo(f"Error: Unsupported format '{format}'", err=True)
            return
        
        click.echo(f"Loaded {len(listings)} listings")
        
        # Validate data if requested
        if validate:
            click.echo("Validating listings...")
            valid_listings = []
            for listing in listings:
                if DataProcessor.validate_listing(listing):
                    valid_listings.append(listing)
            
            click.echo(f"Validation: {len(valid_listings)}/{len(listings)} listings are valid")
            listings = valid_listings
        
        # Deduplicate if requested
        if deduplicate:
            click.echo("Removing duplicates...")
            original_count = len(listings)
            listings = DataProcessor.deduplicate_listings(listings)
            click.echo(f"Deduplication: {original_count} -> {len(listings)} listings")
        
        # Generate report if requested
        if report:
            click.echo("\nGenerating data quality report...")
            report_data = DataProcessor.generate_report(listings)
            
            click.echo("\n=== DATA QUALITY REPORT ===")
            click.echo(f"Total listings: {report_data['total_listings']}")
            click.echo(f"Sources: {report_data['sources']}")
            click.echo("\nData completeness:")
            for key, value in report_data['data_completeness'].items():
                click.echo(f"  {key}: {value}")
            
            if report_data.get('price_statistics'):
                click.echo("\nPrice statistics (ZAR):")
                stats = report_data['price_statistics']
                click.echo(f"  Min: R{stats['min']:,.0f}")
                click.echo(f"  Max: R{stats['max']:,.0f}")
                click.echo(f"  Average: R{stats['average']:,.0f}")
                click.echo(f"  Median: R{stats['median']:,.0f}")
        
        # Process based on target
        if target == 'database':
            click.echo(f"Seeding database: {db_path}")
            seeder = DatabaseSeeder(db_path)
            inserted_count = seeder.seed_listings(listings)
            click.echo(f"Successfully inserted {inserted_count} listings into database")
        
        elif target == 'json':
            output_file = output or 'processed_listings.json'
            click.echo(f"Exporting to JSON: {output_file}")
            DataProcessor.export_to_json(listings, output_file)
            click.echo(f"Successfully exported {len(listings)} listings to {output_file}")
        
        elif target == 'csv':
            output_file = output or 'processed_listings.csv'
            click.echo(f"Exporting to CSV: {output_file}")
            DataProcessor.export_to_csv(listings, output_file)
            click.echo(f"Successfully exported {len(listings)} listings to {output_file}")
        
        click.echo("✅ Import/seed operation completed successfully!")
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        logger.error(f"Import/seed failed: {e}")


if __name__ == '__main__':
    main()

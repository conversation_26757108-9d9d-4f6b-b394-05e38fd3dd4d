#!/usr/bin/env python3
"""
Debug script to capture and save HTML output from Student24.co
This will help us understand the website structure and update the scraper accordingly
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
import time
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def capture_html_output():
    """Capture HTML output from Student24.co for analysis"""
    
    print("🔍 Capturing HTML Output from Student24.co")
    print("=" * 60)
    
    # Create debug output directory
    debug_dir = Path("debug_html")
    debug_dir.mkdir(exist_ok=True)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    # URLs to test
    test_urls = [
        ("main_page", "https://student24.co/"),
        ("uj_accommodation", "https://student24.co/accommodation?ai=qnht100txslt224txuzt127"),
        ("uct_accommodation", "https://student24.co/accommodation?ai=uct"),
        ("wits_accommodation", "https://student24.co/accommodation?ai=wits"),
        ("up_accommodation", "https://student24.co/accommodation?ai=up"),
        ("stellenbosch_accommodation", "https://student24.co/accommodation?ai=stellenbosch"),
        ("general_accommodation", "https://student24.co/accommodation"),
    ]
    
    results = {}
    
    for name, url in test_urls:
        print(f"\n📡 Fetching: {name}")
        print(f"URL: {url}")
        
        try:
            response = session.get(url, timeout=15)
            print(f"Status: {response.status_code}")
            print(f"Content-Length: {len(response.content)} bytes")
            
            if response.status_code == 200:
                # Save raw HTML
                html_file = debug_dir / f"{name}_raw.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                # Parse with BeautifulSoup and save prettified version
                soup = BeautifulSoup(response.content, 'html.parser')
                pretty_file = debug_dir / f"{name}_pretty.html"
                with open(pretty_file, 'w', encoding='utf-8') as f:
                    f.write(soup.prettify())
                
                # Extract and save key information
                info = analyze_page_content(soup, url)
                results[name] = info
                
                # Save analysis
                analysis_file = debug_dir / f"{name}_analysis.txt"
                with open(analysis_file, 'w', encoding='utf-8') as f:
                    f.write(f"URL: {url}\n")
                    f.write(f"Status: {response.status_code}\n")
                    f.write(f"Content-Length: {len(response.content)} bytes\n\n")
                    f.write("=== PAGE ANALYSIS ===\n")
                    for key, value in info.items():
                        f.write(f"{key}: {value}\n")
                
                print(f"✅ Saved: {html_file}, {pretty_file}, {analysis_file}")
                
                # Show key findings
                print(f"Title: {info.get('title', 'N/A')}")
                print(f"Accommodation keywords: {info.get('accommodation_keywords', 0)}")
                print(f"Search input found: {info.get('has_search_input', False)}")
                print(f"Potential listings: {info.get('potential_listings', 0)}")
                
            else:
                print(f"❌ Failed with status {response.status_code}")
                results[name] = {"error": f"HTTP {response.status_code}"}
            
            # Be respectful with delays
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ Error: {e}")
            results[name] = {"error": str(e)}
    
    # Generate summary report
    print(f"\n" + "=" * 60)
    print("📋 SUMMARY REPORT")
    print("=" * 60)
    
    summary_file = debug_dir / "summary_report.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("Student24.co HTML Analysis Summary\n")
        f.write("=" * 40 + "\n\n")
        
        for name, info in results.items():
            print(f"\n🔍 {name.upper()}:")
            f.write(f"{name.upper()}:\n")
            
            if "error" in info:
                print(f"  ❌ Error: {info['error']}")
                f.write(f"  Error: {info['error']}\n")
            else:
                print(f"  Title: {info.get('title', 'N/A')}")
                print(f"  Has search input: {info.get('has_search_input', False)}")
                print(f"  Accommodation content: {info.get('accommodation_keywords', 0)} keywords")
                print(f"  Potential listings: {info.get('potential_listings', 0)}")
                print(f"  JavaScript detected: {info.get('has_javascript', False)}")
                
                f.write(f"  Title: {info.get('title', 'N/A')}\n")
                f.write(f"  Has search input: {info.get('has_search_input', False)}\n")
                f.write(f"  Accommodation content: {info.get('accommodation_keywords', 0)} keywords\n")
                f.write(f"  Potential listings: {info.get('potential_listings', 0)}\n")
                f.write(f"  JavaScript detected: {info.get('has_javascript', False)}\n")
            
            f.write("\n")
    
    print(f"\n💾 All files saved to: {debug_dir}")
    print(f"📄 Summary report: {summary_file}")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    
    main_page_info = results.get('main_page', {})
    if main_page_info.get('has_search_input'):
        print("✅ Search input found - we can use it for location searches")
    else:
        print("❌ No search input found - may need different approach")
    
    if any(info.get('has_javascript', False) for info in results.values()):
        print("⚠️  JavaScript detected - Selenium recommended for full functionality")
    
    if any(info.get('potential_listings', 0) > 0 for info in results.values()):
        print("✅ Potential accommodation listings found")
    else:
        print("❌ No obvious accommodation listings found - may be dynamically loaded")
    
    print(f"\n📁 Next steps:")
    print(f"1. Review the HTML files in {debug_dir}")
    print(f"2. Look for accommodation listing patterns")
    print(f"3. Update scraper selectors based on findings")
    print(f"4. Test with Selenium if JavaScript is heavily used")
    
    return results

def analyze_page_content(soup: BeautifulSoup, url: str) -> dict:
    """Analyze page content and extract key information"""
    
    info = {}
    
    # Basic page info
    info['title'] = soup.title.string if soup.title else "No title"
    info['url'] = url
    
    # Check for search input
    search_input = soup.find('input', {'id': 'location'})
    info['has_search_input'] = search_input is not None
    if search_input:
        info['search_placeholder'] = search_input.get('placeholder', '')
    
    # Count accommodation-related keywords
    page_text = soup.get_text().lower()
    accommodation_keywords = [
        'accommodation', 'residence', 'student housing', 'apartment', 
        'room', 'rental', 'booking', 'available', 'per month'
    ]
    
    keyword_count = sum(page_text.count(keyword) for keyword in accommodation_keywords)
    info['accommodation_keywords'] = keyword_count
    
    # Look for potential listing containers
    potential_containers = [
        soup.find_all('div', class_=lambda x: x and any(word in x.lower() for word in ['card', 'item', 'listing', 'property', 'accommodation'])),
        soup.find_all('article'),
        soup.find_all('div', class_=lambda x: x and 'result' in x.lower())
    ]
    
    total_potential_listings = sum(len(containers) for containers in potential_containers)
    info['potential_listings'] = total_potential_listings
    
    # Check for JavaScript frameworks
    scripts = soup.find_all('script')
    js_frameworks = []
    for script in scripts:
        script_content = script.get_text().lower()
        if 'react' in script_content:
            js_frameworks.append('React')
        if 'vue' in script_content:
            js_frameworks.append('Vue')
        if 'angular' in script_content:
            js_frameworks.append('Angular')
        if 'jquery' in script_content:
            js_frameworks.append('jQuery')
    
    info['has_javascript'] = len(js_frameworks) > 0
    info['js_frameworks'] = js_frameworks
    
    # Look for forms
    forms = soup.find_all('form')
    info['form_count'] = len(forms)
    
    # Look for links
    links = soup.find_all('a', href=True)
    accommodation_links = [link for link in links 
                          if any(keyword in link.get('href', '').lower() or keyword in link.get_text().lower() 
                                for keyword in ['accommodation', 'residence', 'property'])]
    info['accommodation_links'] = len(accommodation_links)
    
    # Look for images
    images = soup.find_all('img')
    info['image_count'] = len(images)
    
    # Check for price information
    price_patterns = [r'R\s*\d+', r'\d+\s*per\s*month', r'price']
    price_mentions = 0
    for pattern in price_patterns:
        import re
        matches = re.findall(pattern, page_text, re.IGNORECASE)
        price_mentions += len(matches)
    info['price_mentions'] = price_mentions
    
    return info

if __name__ == '__main__':
    try:
        results = capture_html_output()
        print("\n🎉 HTML capture completed successfully!")
    except Exception as e:
        print(f"\n❌ Error during HTML capture: {e}")
        sys.exit(1)

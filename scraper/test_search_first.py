#!/usr/bin/env python3
"""
Test the search-first Student24 scraper that actually performs searches
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_search_first_scraper():
    """Test the search-first scraper"""
    
    print("🔍 Testing SEARCH-FIRST Student24 Scraper")
    print("=" * 70)
    print("This scraper actually performs searches before extracting data")
    print()
    
    try:
        from scrapers.student24_search_first_scraper import Student24SearchFirstScraper, SEARCH_FIRST_TERMS
        print("✅ Search-first scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import search-first scraper: {e}")
        return False
    
    try:
        # Create output directory
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # Initialize scraper
        print("🚀 Initializing search-first scraper...")
        scraper = Student24SearchFirstScraper(delay=2)
        
        # Show what we'll search for
        print(f"\n🎯 Will search for {len(SEARCH_FIRST_TERMS)} university terms:")
        for i, term in enumerate(SEARCH_FIRST_TERMS, 1):
            print(f"  {i}. {term}")
        
        # Perform searches and scrape
        print(f"\n📡 Starting search-first scraping...")
        print("This will:")
        print("1. Search for each university term")
        print("2. Extract accommodations from search results") 
        print("3. Save search responses for debugging")
        print()
        
        listings = scraper.scrape_accommodations()
        
        print(f"\n📊 SEARCH-FIRST SCRAPING RESULTS:")
        print(f"Total accommodations found: {len(listings)}")
        
        if listings:
            print(f"\n🏠 SEARCH-BASED ACCOMMODATION DETAILS:")
            print("=" * 70)
            
            for i, listing in enumerate(listings, 1):
                print(f"\n📋 ACCOMMODATION {i}:")
                print(f"  🏷️  Title: {listing.get('title', 'N/A')}")
                print(f"  💰 Price: R{listing.get('price', 0):,.0f} per month")
                print(f"  📍 Location: {listing.get('location', {}).get('name', 'N/A')}")
                
                # Show coordinates if available
                location = listing.get('location', {})
                if location.get('latitude'):
                    print(f"  🗺️  Coordinates: {location['latitude']:.4f}, {location['longitude']:.4f}")
                
                description = listing.get('description', '')
                if description:
                    print(f"  📝 Description: {description[:100]}...")
                
                features = listing.get('features', [])
                if features:
                    print(f"  🏷️  Features: {', '.join(features[:5])}")
                
                contact = listing.get('contact', {})
                print(f"  📞 Contact: {contact.get('type', 'N/A')}")
                
                if listing.get('images'):
                    print(f"  🖼️  Images: {len(listing.get('images', []))} images available")
                
                print(f"  🔗 URL: {listing.get('url', 'N/A')}")
                print(f"  🕒 Scraped: {listing.get('scraped_at', 'N/A')}")
            
            # Save detailed results
            detailed_file = output_dir / "student24_search_first_detailed.json"
            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "accommodations": listings,
                    "metadata": {
                        "total_count": len(listings),
                        "scraper": "student24_search_first",
                        "method": "actual_search_then_extract",
                        "search_terms": SEARCH_FIRST_TERMS,
                        "approach": "search_first_then_scrape"
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Detailed results saved to: {detailed_file}")
            
            # Check for debug files
            debug_dir = Path("debug_search_responses")
            if debug_dir.exists():
                debug_files = list(debug_dir.glob("*.html"))
                print(f"\n🔧 Debug files saved: {len(debug_files)} search responses")
                for debug_file in debug_files[:3]:  # Show first 3
                    print(f"  📄 {debug_file.name}")
                if len(debug_files) > 3:
                    print(f"  ... and {len(debug_files) - 3} more")
            
            # Test data validation
            print(f"\n🔍 DATA VALIDATION:")
            try:
                from utils.data_processor import DataProcessor
                
                # Validate each listing
                valid_listings = []
                for listing in listings:
                    if DataProcessor.validate_listing(listing):
                        valid_listings.append(listing)
                        print(f"  ✅ {listing['title']}: Valid")
                    else:
                        print(f"  ❌ {listing['title']}: Invalid")
                
                print(f"\n📈 VALIDATION SUMMARY:")
                print(f"  Valid listings: {len(valid_listings)}/{len(listings)}")
                
                if valid_listings:
                    # Generate data quality report
                    report = DataProcessor.generate_report(valid_listings)
                    
                    print(f"\n📊 DATA QUALITY REPORT:")
                    print(f"  Sources: {report.get('sources', {})}")
                    print(f"  Data completeness:")
                    for key, value in report.get('data_completeness', {}).items():
                        print(f"    {key}: {value}")
                    
                    if report.get('price_statistics'):
                        stats = report['price_statistics']
                        print(f"  Price statistics:")
                        print(f"    Average: R{stats['average']:,.0f}")
                        print(f"    Range: R{stats['min']:,.0f} - R{stats['max']:,.0f}")
                    
                    # Show search effectiveness
                    search_effectiveness = {}
                    for listing in valid_listings:
                        source_url = listing.get('url', '')
                        if 'accommodation?ai=' in source_url:
                            search_effectiveness['direct_accommodation'] = search_effectiveness.get('direct_accommodation', 0) + 1
                        else:
                            search_effectiveness['other'] = search_effectiveness.get('other', 0) + 1
                    
                    print(f"\n🎯 SEARCH EFFECTIVENESS:")
                    for method, count in search_effectiveness.items():
                        print(f"    {method}: {count} accommodations")
                    
                    # Save validated results
                    validated_file = output_dir / "student24_search_first_validated.json"
                    with open(validated_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            "accommodations": valid_listings,
                            "report": report,
                            "search_effectiveness": search_effectiveness,
                            "metadata": {
                                "total_count": len(valid_listings),
                                "validation_success_rate": f"{len(valid_listings)}/{len(listings)}",
                                "scraper": "student24_search_first",
                                "search_method": "actual_university_searches"
                            }
                        }, f, indent=2, ensure_ascii=False)
                    
                    print(f"\n💾 Validated results saved to: {validated_file}")
                
            except Exception as e:
                print(f"⚠️  Data validation error: {e}")
            
            # Clean up
            scraper.close()
            
            return True
        
        else:
            print("❌ No accommodations found through searches")
            print("\nPossible reasons:")
            print("1. Search functionality returns empty results")
            print("2. Dynamic content loading not captured")
            print("3. Search parameters not working")
            print("4. Website blocking search requests")
            
            print("\n🔧 Troubleshooting:")
            print("1. Check debug_search_responses/ for search results")
            print("2. Verify search URLs are working")
            print("3. Compare with manual browser search")
            print("4. Check if search requires JavaScript")
            
            # Check if debug files were created
            debug_dir = Path("debug_search_responses")
            if debug_dir.exists():
                debug_files = list(debug_dir.glob("*.html"))
                print(f"\n📄 Debug files created: {len(debug_files)}")
                print("Review these files to see what the searches returned")
            
            scraper.close()
            return False
        
    except Exception as e:
        print(f"❌ Error testing search-first scraper: {e}")
        print(f"\n🔧 Error details: {type(e).__name__}: {e}")
        return False

def main():
    """Run the search-first scraper test"""
    
    print("🧪 Student24 SEARCH-FIRST Scraper Test")
    print("=" * 70)
    print("This scraper performs actual searches before extracting data")
    print()
    
    success = test_search_first_scraper()
    
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    if success:
        print("🎉 SUCCESS! Search-first scraper is working")
        print("\nWhat we achieved:")
        print("✅ Performed actual searches for universities")
        print("✅ Extracted accommodations from search results")
        print("✅ Saved search responses for analysis")
        print("✅ Validated and processed data")
        
        print("\nSearch-first approach benefits:")
        print("📄 Mimics real user behavior")
        print("🔍 Actually searches before extracting")
        print("📊 Shows what each search returns")
        print("🔧 Provides debug information")
        
        print("\nNext steps:")
        print("1. Review debug files to optimize searches")
        print("2. Use this scraper in production")
        print("3. Add more search terms as needed")
        
    else:
        print("❌ Test failed")
        print("\nTroubleshooting:")
        print("1. Check debug_search_responses/ directory")
        print("2. Compare search results with browser")
        print("3. Verify search functionality works")
        print("4. Check if JavaScript is required")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

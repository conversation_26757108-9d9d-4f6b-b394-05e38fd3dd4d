"""
Data processing utilities for cleaning and validating scraped property data
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

try:
    import pandas as pd
except ImportError:
    print("Warning: pandas not available. CSV export will be limited.")
    pd = None

try:
    from jsonschema import validate, ValidationError
except ImportError:
    print("Warning: jsonschema not available. Validation will be skipped.")
    def validate(instance, schema):
        return True
    class ValidationError(Exception):
        pass

logger = logging.getLogger(__name__)


class DataProcessor:
    """Utility class for processing and validating scraped property data"""
    
    # JSON schema for property listing validation
    LISTING_SCHEMA = {
        "type": "object",
        "required": ["id", "source", "title", "price", "location"],
        "properties": {
            "id": {"type": "string"},
            "source": {"type": "string", "enum": ["gumtree", "property24", "privateproperty", "student_com", "campus_stay", "student24", "university_residence"]},
            "title": {"type": "string", "minLength": 1},
            "price": {"type": "number", "minimum": 0},
            "currency": {"type": "string", "default": "ZAR"},
            "location": {
                "type": "object",
                "required": ["name"],
                "properties": {
                    "name": {"type": "string"},
                    "address": {"type": "string"},
                    "latitude": {"type": "number"},
                    "longitude": {"type": "number"}
                }
            },
            "description": {"type": "string"},
            "features": {"type": "array", "items": {"type": "string"}},
            "images": {"type": "array", "items": {"type": "string"}},
            "contact": {
                "type": "object",
                "properties": {
                    "type": {"type": "string", "enum": ["whatsapp", "phone", "email", "website"]},
                    "value": {"type": "string"}
                }
            },
            "scraped_at": {"type": "string"},
            "url": {"type": "string"}
        }
    }
    
    @staticmethod
    def clean_price(price_text: str) -> Optional[float]:
        """Extract numeric price from text"""
        if not price_text:
            return None
        
        # Remove common currency symbols and text
        cleaned = re.sub(r'[R\s,]', '', str(price_text))
        cleaned = re.sub(r'(per month|pm|p/m|monthly)', '', cleaned, flags=re.IGNORECASE)
        
        # Extract first number found
        match = re.search(r'(\d+(?:\.\d+)?)', cleaned)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                pass
        
        return None
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', str(text).strip())
        
        # Remove common unwanted characters
        cleaned = re.sub(r'[^\w\s\-.,!?()&]', '', cleaned)
        
        return cleaned
    
    @staticmethod
    def extract_features(description: str) -> List[str]:
        """Extract property features from description text"""
        if not description:
            return []
        
        features = []
        text = description.lower()
        
        # Common property features to look for
        feature_patterns = {
            'bedrooms': r'(\d+)\s*(?:bed|bedroom)',
            'bathrooms': r'(\d+)\s*(?:bath|bathroom)',
            'parking': r'(parking|garage|carport)',
            'garden': r'(garden|yard)',
            'pool': r'(pool|swimming)',
            'security': r'(security|alarm|gate)',
            'furnished': r'(furnished|furniture)',
            'wifi': r'(wifi|internet|broadband)',
            'aircon': r'(air.?con|aircon|a/c)',
            'kitchen': r'(kitchen|kitchenette)',
            'laundry': r'(laundry|washing)',
        }
        
        for feature_name, pattern in feature_patterns.items():
            match = re.search(pattern, text)
            if match:
                if feature_name in ['bedrooms', 'bathrooms']:
                    features.append(f"{match.group(1)} {feature_name}")
                else:
                    features.append(feature_name)
        
        return features
    
    @staticmethod
    def extract_contact_info(text: str) -> Optional[Dict[str, str]]:
        """Extract contact information from text"""
        if not text:
            return None
        
        # WhatsApp patterns
        whatsapp_patterns = [
            r'wa\.me/(\d+)',
            r'whatsapp.*?(\d{10,})',
            r'wa.*?(\d{10,})'
        ]
        
        for pattern in whatsapp_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                number = match.group(1)
                return {
                    "type": "whatsapp",
                    "value": f"https://wa.me/{number}"
                }
        
        # Phone number patterns
        phone_patterns = [
            r'(\+27\d{9})',
            r'(0\d{9})',
            r'(\d{3}[-\s]?\d{3}[-\s]?\d{4})'
        ]
        
        for pattern in phone_patterns:
            match = re.search(pattern, text)
            if match:
                return {
                    "type": "phone",
                    "value": match.group(1)
                }
        
        # Email patterns
        email_match = re.search(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', text)
        if email_match:
            return {
                "type": "email",
                "value": email_match.group(1)
            }
        
        return None
    
    @staticmethod
    def validate_listing(listing: Dict[str, Any]) -> bool:
        """Validate a listing against the schema"""
        try:
            validate(instance=listing, schema=DataProcessor.LISTING_SCHEMA)
            return True
        except ValidationError as e:
            logger.warning(f"Listing validation failed: {e.message}")
            return False
    
    @staticmethod
    def deduplicate_listings(listings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate listings based on title and price similarity"""
        if not listings:
            return []
        
        unique_listings = []
        seen_combinations = set()
        
        for listing in listings:
            # Create a key based on title and price for deduplication
            title = DataProcessor.clean_text(listing.get('title', '')).lower()
            price = listing.get('price', 0)
            location = listing.get('location', {}).get('name', '').lower()
            
            key = f"{title[:50]}_{price}_{location}"
            
            if key not in seen_combinations:
                seen_combinations.add(key)
                unique_listings.append(listing)
            else:
                logger.debug(f"Duplicate listing removed: {title[:50]}")
        
        logger.info(f"Deduplication: {len(listings)} -> {len(unique_listings)} listings")
        return unique_listings
    
    @staticmethod
    def export_to_csv(listings: List[Dict[str, Any]], filename: str) -> None:
        """Export listings to CSV format"""
        if not listings:
            logger.warning("No listings to export")
            return

        if pd is None:
            logger.error("pandas not available. Cannot export to CSV.")
            return

        # Flatten the nested structure for CSV
        flattened = []
        for listing in listings:
            flat = {
                'id': listing.get('id'),
                'source': listing.get('source'),
                'title': listing.get('title'),
                'price': listing.get('price'),
                'currency': listing.get('currency', 'ZAR'),
                'location_name': listing.get('location', {}).get('name'),
                'location_address': listing.get('location', {}).get('address'),
                'latitude': listing.get('location', {}).get('latitude'),
                'longitude': listing.get('location', {}).get('longitude'),
                'description': listing.get('description'),
                'features': ', '.join(listing.get('features', [])),
                'images': ', '.join(listing.get('images', [])),
                'contact_type': listing.get('contact', {}).get('type'),
                'contact_value': listing.get('contact', {}).get('value'),
                'scraped_at': listing.get('scraped_at'),
                'url': listing.get('url')
            }
            flattened.append(flat)

        df = pd.DataFrame(flattened)
        df.to_csv(filename, index=False)
        logger.info(f"Exported {len(listings)} listings to {filename}")
    
    @staticmethod
    def export_to_json(listings: List[Dict[str, Any]], filename: str) -> None:
        """Export listings to JSON format"""
        output = {
            "listings": listings,
            "metadata": {
                "total_count": len(listings),
                "exported_at": datetime.now().isoformat(),
                "sources": list(set(listing.get('source') for listing in listings))
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Exported {len(listings)} listings to {filename}")
    
    @staticmethod
    def generate_report(listings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a data quality report"""
        if not listings:
            return {"error": "No listings to analyze"}
        
        total = len(listings)
        
        # Count by source
        sources = {}
        for listing in listings:
            source = listing.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        # Data completeness
        with_coordinates = sum(1 for l in listings 
                             if l.get('location', {}).get('latitude') is not None)
        with_contact = sum(1 for l in listings if l.get('contact'))
        with_images = sum(1 for l in listings if l.get('images'))
        with_features = sum(1 for l in listings if l.get('features'))
        
        # Price statistics
        prices = [l.get('price') for l in listings if l.get('price')]
        price_stats = {}
        if prices:
            price_stats = {
                "min": min(prices),
                "max": max(prices),
                "average": sum(prices) / len(prices),
                "median": sorted(prices)[len(prices) // 2]
            }
        
        return {
            "total_listings": total,
            "sources": sources,
            "data_completeness": {
                "with_coordinates": f"{with_coordinates}/{total} ({with_coordinates/total*100:.1f}%)",
                "with_contact": f"{with_contact}/{total} ({with_contact/total*100:.1f}%)",
                "with_images": f"{with_images}/{total} ({with_images/total*100:.1f}%)",
                "with_features": f"{with_features}/{total} ({with_features/total*100:.1f}%)"
            },
            "price_statistics": price_stats
        }

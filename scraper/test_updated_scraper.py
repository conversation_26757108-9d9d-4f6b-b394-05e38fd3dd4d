#!/usr/bin/env python3
"""
Test script for the updated Student24 scraper
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_updated_scraper():
    """Test the updated Student24 scraper"""
    
    print("🚀 Testing Updated Student24 Scraper")
    print("=" * 60)
    print("This scraper uses the search input and handles dynamic content")
    print()
    
    try:
        from scrapers.student24_updated_scraper import Student24UpdatedScraper
        print("✅ Updated scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import updated scraper: {e}")
        return False
    
    try:
        # Create output directory first
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)

        # Test with Selenium first (if available)
        print("\n🤖 Testing with Selenium (dynamic content)...")
        scraper_selenium = Student24UpdatedScraper(delay=2, use_selenium=True)
        
        if scraper_selenium.use_selenium:
            print("✅ Selenium mode activated")
            listings_selenium = scraper_selenium.scrape_accommodations()
            
            print(f"\n📊 Selenium Results:")
            print(f"  Total listings found: {len(listings_selenium)}")
            
            if listings_selenium:
                for i, listing in enumerate(listings_selenium[:3]):  # Show first 3
                    print(f"\n  📋 Listing {i+1}:")
                    print(f"    Title: {listing.get('title', 'N/A')}")
                    print(f"    Price: R{listing.get('price', 0):,.0f}")
                    print(f"    Location: {listing.get('location', {}).get('name', 'N/A')}")
                    print(f"    URL: {listing.get('url', 'N/A')}")
                
                # Save Selenium results
                
                selenium_file = output_dir / "selenium_updated_results.json"
                with open(selenium_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "listings": listings_selenium,
                        "metadata": {
                            "total_count": len(listings_selenium),
                            "scraper": "student24_updated_selenium",
                            "method": "selenium_search"
                        }
                    }, f, indent=2, ensure_ascii=False)
                
                print(f"\n💾 Selenium results saved to: {selenium_file}")
            
            selenium_success = len(listings_selenium) > 0
        else:
            print("⚠️  Selenium not available, skipping...")
            selenium_success = False
            listings_selenium = []
        
        # Test with requests fallback
        print(f"\n📡 Testing with Requests (fallback mode)...")
        scraper_requests = Student24UpdatedScraper(delay=1, use_selenium=False)
        listings_requests = scraper_requests.scrape_accommodations()
        
        print(f"\n📊 Requests Results:")
        print(f"  Total listings found: {len(listings_requests)}")
        
        if listings_requests:
            for i, listing in enumerate(listings_requests):
                print(f"\n  📋 Listing {i+1}:")
                print(f"    Title: {listing.get('title', 'N/A')}")
                print(f"    Location: {listing.get('location', {}).get('name', 'N/A')}")
                print(f"    URL: {listing.get('url', 'N/A')}")
            
            # Save requests results
            requests_file = output_dir / "requests_updated_results.json"
            with open(requests_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "listings": listings_requests,
                    "metadata": {
                        "total_count": len(listings_requests),
                        "scraper": "student24_updated_requests",
                        "method": "direct_urls"
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Requests results saved to: {requests_file}")
        
        requests_success = len(listings_requests) > 0
        
        # Combine results and validate
        all_listings = listings_selenium + listings_requests
        
        if all_listings:
            print(f"\n🔍 Data Validation:")
            
            try:
                from utils.data_processor import DataProcessor
                
                # Validate listings
                valid_count = sum(1 for listing in all_listings if DataProcessor.validate_listing(listing))
                print(f"  ✅ Validation: {valid_count}/{len(all_listings)} listings are valid")
                
                # Deduplicate
                deduplicated = DataProcessor.deduplicate_listings(all_listings)
                print(f"  🔄 Deduplication: {len(all_listings)} -> {len(deduplicated)} unique listings")
                
                # Generate report
                if deduplicated:
                    report = DataProcessor.generate_report(deduplicated)
                    print(f"\n📈 Data Quality Report:")
                    print(f"  Sources: {report.get('sources', {})}")
                    for key, value in report.get('data_completeness', {}).items():
                        print(f"  {key}: {value}")
                
                # Save combined results
                combined_file = output_dir / "student24_updated_combined.json"
                with open(combined_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "listings": deduplicated,
                        "metadata": {
                            "total_count": len(deduplicated),
                            "selenium_count": len(listings_selenium),
                            "requests_count": len(listings_requests),
                            "scraper": "student24_updated_combined"
                        }
                    }, f, indent=2, ensure_ascii=False)
                
                print(f"\n💾 Combined results saved to: {combined_file}")
                
            except Exception as e:
                print(f"⚠️  Data processing error: {e}")
        
        return selenium_success or requests_success
        
    except Exception as e:
        print(f"❌ Error testing updated scraper: {e}")
        return False

def main():
    """Run the updated scraper test"""
    
    print("🧪 Student24 Updated Scraper Test")
    print("=" * 60)
    
    success = test_updated_scraper()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS! Updated scraper is working")
        print("\nWhat we achieved:")
        print("✅ Can connect to Student24.co")
        print("✅ Can use the search functionality")
        print("✅ Can extract accommodation data")
        print("✅ Handles both Selenium and requests modes")
        
        print("\nNext steps:")
        print("1. Install Chrome for full Selenium functionality:")
        print("   sudo apt-get install google-chrome-stable")
        print("2. Scale up the scraping")
        print("3. Integrate with main scraper")
        
        print("\nTo use in production:")
        print("python3 main.py --sites student24_updated")
        
    else:
        print("❌ Tests failed")
        print("\nTroubleshooting:")
        print("1. Check internet connectivity")
        print("2. Install Chrome for Selenium support")
        print("3. Check if Student24.co is accessible")
        print("4. Review debug HTML files for structure changes")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

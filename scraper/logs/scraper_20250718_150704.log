2025-07-18 15:07:04,213 - __main__ - INFO - Starting South African Property Scraper
2025-07-18 15:07:04,213 - __main__ - INFO - Sites: ['student24_hybrid']
2025-07-18 15:07:04,213 - __main__ - INFO - Max pages per site: 3
2025-07-18 15:07:04,213 - __main__ - INFO - Delay: 3.0s
2025-07-18 15:07:04,213 - __main__ - INFO - Max retries: 3
2025-07-18 15:07:04,214 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,214 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,215 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,215 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,216 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,216 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,216 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,216 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:04,216 - WDM - INFO - ====== WebDriver manager ======
2025-07-18 15:07:04,468 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-18 15:07:04,922 - WDM - INFO - About to download new driver from https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_linux64.zip
2025-07-18 15:07:05,253 - WDM - INFO - Driver downloading response is 200
2025-07-18 15:07:07,224 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-18 15:07:08,018 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-18 15:07:08,420 - WDM - INFO - Driver has been saved in cache [/home/<USER>/.wdm/drivers/chromedriver/linux64/114.0.5735.90]
2025-07-18 15:07:08,550 - scrapers.student24_hybrid_scraper - WARNING - Selenium setup failed, falling back to requests: Message: unknown error: cannot find Chrome binary
Stacktrace:
#0 0x55d9adebb4e3 <unknown>
#1 0x55d9adbeac76 <unknown>
#2 0x55d9adc11757 <unknown>
#3 0x55d9adc10029 <unknown>
#4 0x55d9adc4eccc <unknown>
#5 0x55d9adc4e47f <unknown>
#6 0x55d9adc45de3 <unknown>
#7 0x55d9adc1b2dd <unknown>
#8 0x55d9adc1c34e <unknown>
#9 0x55d9ade7b3e4 <unknown>
#10 0x55d9ade7f3d7 <unknown>
#11 0x55d9ade89b20 <unknown>
#12 0x55d9ade80023 <unknown>
#13 0x55d9ade4e1aa <unknown>
#14 0x55d9adea46b8 <unknown>
#15 0x55d9adea4847 <unknown>
#16 0x55d9adeb4243 <unknown>
#17 0x7fad8f26fac3 <unknown>

2025-07-18 15:07:08,551 - scrapers.student24_hybrid_scraper - INFO - Using requests+BeautifulSoup for scraping
2025-07-18 15:07:08,551 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:07:08,551 - __main__ - INFO - 
==================================================
2025-07-18 15:07:08,551 - __main__ - INFO - SCRAPING: STUDENT24_HYBRID
2025-07-18 15:07:08,551 - __main__ - INFO - ==================================================
2025-07-18 15:07:08,551 - __main__ - INFO - Starting to scrape student24_hybrid with 6 base URLs
2025-07-18 15:07:08,551 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 1/6: https://student24.co/accommodation?ai=qnht100txslt224txuzt127
2025-07-18 15:07:09,658 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=qnht100txslt224txuzt127
2025-07-18 15:07:12,662 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 2/6: https://student24.co/accommodation?ai=uct
2025-07-18 15:07:13,006 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=uct
2025-07-18 15:07:16,009 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 3/6: https://student24.co/accommodation?ai=wits
2025-07-18 15:07:16,393 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=wits
2025-07-18 15:07:19,396 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 4/6: https://student24.co/accommodation?ai=up
2025-07-18 15:07:19,776 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=up
2025-07-18 15:07:22,779 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 5/6: https://student24.co/accommodation?ai=stellenbosch
2025-07-18 15:07:23,172 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=stellenbosch
2025-07-18 15:07:26,175 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 6/6: https://student24.co/accommodation
2025-07-18 15:07:26,519 - utils.geocoding - WARNING - No coordinates found for: South Africa
2025-07-18 15:07:26,519 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation
2025-07-18 15:07:26,521 - scrapers.student24_hybrid_scraper - INFO - Total accommodations scraped: 6
2025-07-18 15:07:26,521 - __main__ - INFO - Successfully scraped 6 listings from student24_hybrid
2025-07-18 15:07:26,521 - __main__ - INFO - Completed student24_hybrid: 6 listings
2025-07-18 15:07:26,521 - __main__ - INFO - Total listings so far: 6
2025-07-18 15:07:26,521 - __main__ - INFO - Processing 6 listings...
2025-07-18 15:07:26,521 - utils.data_processor - INFO - Deduplication: 6 -> 5 listings
2025-07-18 15:07:26,521 - __main__ - INFO - Deduplication: 6 -> 5 listings
2025-07-18 15:07:26,522 - utils.data_processor - INFO - Exported 5 listings to output/listings_20250718_150726.json
2025-07-18 15:07:26,527 - utils.data_processor - INFO - Exported 5 listings to output/listings_20250718_150726.csv
2025-07-18 15:07:26,527 - __main__ - INFO - Files saved:
2025-07-18 15:07:26,528 - __main__ - INFO -   JSON: output/listings_20250718_150726.json
2025-07-18 15:07:26,528 - __main__ - INFO -   CSV: output/listings_20250718_150726.csv
2025-07-18 15:07:26,528 - __main__ - INFO -   Report: output/report_20250718_150726.json
2025-07-18 15:07:26,528 - __main__ - INFO - Scraping completed successfully!

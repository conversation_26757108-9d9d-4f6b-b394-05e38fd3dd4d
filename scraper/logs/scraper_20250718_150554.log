2025-07-18 15:05:54,521 - __main__ - INFO - Starting South African Property Scraper
2025-07-18 15:05:54,522 - __main__ - INFO - Sites: ['student24_hybrid']
2025-07-18 15:05:54,522 - __main__ - INFO - Max pages per site: 1
2025-07-18 15:05:54,522 - __main__ - INFO - Delay: 2.0s
2025-07-18 15:05:54,522 - __main__ - INFO - Max retries: 3
2025-07-18 15:05:54,523 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,524 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,524 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,524 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,525 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,525 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,525 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,526 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:54,526 - WDM - INFO - ====== WebDriver manager ======
2025-07-18 15:05:54,796 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-18 15:05:55,044 - WDM - INFO - About to download new driver from https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_linux64.zip
2025-07-18 15:05:55,383 - WDM - INFO - Driver downloading response is 200
2025-07-18 15:05:57,664 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-18 15:05:58,387 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-18 15:05:58,776 - WDM - INFO - Driver has been saved in cache [/home/<USER>/.wdm/drivers/chromedriver/linux64/114.0.5735.90]
2025-07-18 15:05:58,899 - scrapers.student24_hybrid_scraper - WARNING - Selenium setup failed, falling back to requests: Message: unknown error: cannot find Chrome binary
Stacktrace:
#0 0x560b810974e3 <unknown>
#1 0x560b80dc6c76 <unknown>
#2 0x560b80ded757 <unknown>
#3 0x560b80dec029 <unknown>
#4 0x560b80e2accc <unknown>
#5 0x560b80e2a47f <unknown>
#6 0x560b80e21de3 <unknown>
#7 0x560b80df72dd <unknown>
#8 0x560b80df834e <unknown>
#9 0x560b810573e4 <unknown>
#10 0x560b8105b3d7 <unknown>
#11 0x560b81065b20 <unknown>
#12 0x560b8105c023 <unknown>
#13 0x560b8102a1aa <unknown>
#14 0x560b810806b8 <unknown>
#15 0x560b81080847 <unknown>
#16 0x560b81090243 <unknown>
#17 0x7f2301685ac3 <unknown>

2025-07-18 15:05:58,900 - scrapers.student24_hybrid_scraper - INFO - Using requests+BeautifulSoup for scraping
2025-07-18 15:05:58,900 - utils.geocoding - INFO - Geocoding service initialized with fallback coordinates only
2025-07-18 15:05:58,900 - __main__ - INFO - 
==================================================
2025-07-18 15:05:58,900 - __main__ - INFO - SCRAPING: STUDENT24_HYBRID
2025-07-18 15:05:58,900 - __main__ - INFO - ==================================================
2025-07-18 15:05:58,900 - __main__ - INFO - Starting to scrape student24_hybrid with 6 base URLs
2025-07-18 15:05:58,900 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 1/6: https://student24.co/accommodation?ai=qnht100txslt224txuzt127
2025-07-18 15:06:00,466 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=qnht100txslt224txuzt127
2025-07-18 15:06:02,468 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 2/6: https://student24.co/accommodation?ai=uct
2025-07-18 15:06:02,878 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=uct
2025-07-18 15:06:04,880 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 3/6: https://student24.co/accommodation?ai=wits
2025-07-18 15:06:05,231 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=wits
2025-07-18 15:06:07,234 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 4/6: https://student24.co/accommodation?ai=up
2025-07-18 15:06:07,631 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=up
2025-07-18 15:06:09,633 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 5/6: https://student24.co/accommodation?ai=stellenbosch
2025-07-18 15:06:10,029 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation?ai=stellenbosch
2025-07-18 15:06:12,032 - scrapers.student24_hybrid_scraper - INFO - Scraping URL 6/6: https://student24.co/accommodation
2025-07-18 15:06:12,412 - utils.geocoding - WARNING - No coordinates found for: South Africa
2025-07-18 15:06:12,412 - scrapers.student24_hybrid_scraper - INFO - Found 1 accommodations on https://student24.co/accommodation
2025-07-18 15:06:12,413 - scrapers.student24_hybrid_scraper - INFO - Total accommodations scraped: 6
2025-07-18 15:06:12,413 - __main__ - INFO - Successfully scraped 6 listings from student24_hybrid
2025-07-18 15:06:12,413 - __main__ - INFO - Completed student24_hybrid: 6 listings
2025-07-18 15:06:12,413 - __main__ - INFO - Total listings so far: 6
2025-07-18 15:06:12,413 - __main__ - INFO - Processing 6 listings...
2025-07-18 15:06:12,414 - utils.data_processor - INFO - Deduplication: 6 -> 5 listings
2025-07-18 15:06:12,414 - __main__ - INFO - Deduplication: 6 -> 5 listings
2025-07-18 15:06:12,415 - utils.data_processor - INFO - Exported 5 listings to output/listings_20250718_150612.json
2025-07-18 15:06:12,434 - utils.data_processor - INFO - Exported 5 listings to output/listings_20250718_150612.csv
2025-07-18 15:06:12,434 - __main__ - INFO - Files saved:
2025-07-18 15:06:12,435 - __main__ - INFO -   JSON: output/listings_20250718_150612.json
2025-07-18 15:06:12,435 - __main__ - INFO -   CSV: output/listings_20250718_150612.csv
2025-07-18 15:06:12,435 - __main__ - INFO -   Report: output/report_20250718_150612.json
2025-07-18 15:06:12,435 - __main__ - INFO - Scraping completed successfully!

#!/usr/bin/env python3
"""
Explore Student24.co website structure to find the correct URLs and selectors
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def explore_student24():
    """Explore the Student24.co website to understand its structure"""
    
    print("🔍 Exploring Student24.co Website Structure")
    print("=" * 60)
    
    base_url = "https://student24.co/"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"📡 Fetching main page: {base_url}")
        response = requests.get(base_url, headers=headers, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch main page: {response.status_code}")
            return
        
        print(f"✅ Successfully fetched main page ({len(response.content)} bytes)")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract page title
        title = soup.title.string if soup.title else "No title"
        print(f"📄 Page title: {title}")
        
        # Look for navigation links
        print("\n🔗 Navigation Links Found:")
        nav_links = []
        
        # Common navigation selectors
        nav_selectors = ['nav', '.nav', '.navigation', '.menu', 'header']
        
        for selector in nav_selectors:
            nav_elements = soup.select(selector)
            for nav in nav_elements:
                links = nav.find_all('a', href=True)
                for link in links:
                    href = link.get('href')
                    text = link.get_text().strip()
                    if href and text:
                        full_url = urljoin(base_url, href)
                        if full_url not in nav_links:
                            nav_links.append((text, full_url))
        
        # Also look for all links on the page
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link.get('href')
            text = link.get_text().strip()
            if href and text and len(text) < 50:
                full_url = urljoin(base_url, href)
                if any(keyword in text.lower() for keyword in 
                      ['accommodation', 'property', 'listing', 'search', 'browse', 'find']):
                    nav_links.append((text, full_url))
        
        # Remove duplicates
        unique_links = []
        seen_urls = set()
        for text, url in nav_links:
            if url not in seen_urls:
                unique_links.append((text, url))
                seen_urls.add(url)
        
        for text, url in unique_links[:15]:  # Show first 15
            print(f"  📎 {text}: {url}")
        
        # Look for accommodation-related content
        print("\n🏠 Looking for Accommodation Content:")
        
        # Search for accommodation-related keywords in the page
        page_text = soup.get_text().lower()
        accommodation_keywords = [
            'accommodation', 'residence', 'student housing', 'apartment', 
            'room', 'flat', 'property', 'rental', 'booking'
        ]
        
        found_keywords = []
        for keyword in accommodation_keywords:
            if keyword in page_text:
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"  ✅ Found keywords: {', '.join(found_keywords)}")
        else:
            print("  ❌ No accommodation keywords found")
        
        # Look for forms or search functionality
        print("\n🔍 Search/Form Elements:")
        
        forms = soup.find_all('form')
        if forms:
            print(f"  📝 Found {len(forms)} form(s)")
            for i, form in enumerate(forms):
                action = form.get('action', 'No action')
                method = form.get('method', 'GET')
                print(f"    Form {i+1}: {method} -> {action}")
        
        # Look for search inputs
        search_inputs = soup.find_all('input', {'type': ['search', 'text']})
        for inp in search_inputs:
            name = inp.get('name', 'No name')
            placeholder = inp.get('placeholder', 'No placeholder')
            print(f"  🔍 Search input: {name} ({placeholder})")
        
        # Look for buttons that might lead to listings
        buttons = soup.find_all(['button', 'a'], string=re.compile(r'search|find|browse|view|list', re.I))
        if buttons:
            print(f"  🔘 Found {len(buttons)} relevant button(s)")
            for button in buttons[:5]:
                text = button.get_text().strip()
                href = button.get('href', 'No href')
                print(f"    Button: '{text}' -> {href}")
        
        # Look for any data attributes or JavaScript that might indicate dynamic content
        print("\n⚙️ Dynamic Content Indicators:")
        
        # Look for data attributes
        elements_with_data = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        if elements_with_data:
            print(f"  📊 Found {len(elements_with_data)} elements with data attributes")
        
        # Look for script tags
        scripts = soup.find_all('script')
        js_frameworks = ['react', 'vue', 'angular', 'jquery']
        found_frameworks = []
        
        for script in scripts:
            script_content = script.get_text().lower()
            for framework in js_frameworks:
                if framework in script_content:
                    found_frameworks.append(framework)
        
        if found_frameworks:
            print(f"  🔧 JavaScript frameworks detected: {', '.join(set(found_frameworks))}")
        
        # Look for API endpoints in scripts
        api_patterns = [
            r'/api/[^"\']+',
            r'api\.[^"\']+',
            r'endpoint["\']:\s*["\']([^"\']+)',
        ]
        
        api_endpoints = []
        for script in scripts:
            script_content = script.get_text()
            for pattern in api_patterns:
                matches = re.findall(pattern, script_content)
                api_endpoints.extend(matches)
        
        if api_endpoints:
            print(f"  🔌 Potential API endpoints found: {len(api_endpoints)}")
            for endpoint in api_endpoints[:5]:
                print(f"    API: {endpoint}")
        
        # Summary and recommendations
        print("\n" + "=" * 60)
        print("📋 SUMMARY & RECOMMENDATIONS")
        print("=" * 60)
        
        if found_keywords:
            print("✅ This appears to be a student accommodation website")
        else:
            print("⚠️  This might not be a student accommodation website")
        
        if unique_links:
            print(f"✅ Found {len(unique_links)} navigation links to explore")
        
        if forms or search_inputs:
            print("✅ Website has search functionality")
        
        if found_frameworks:
            print("⚠️  Website uses JavaScript frameworks - might need dynamic scraping")
        
        print("\n🎯 Next Steps:")
        print("1. Try the navigation links found above")
        print("2. Look for search functionality")
        print("3. Check if the site loads content dynamically")
        print("4. Consider using Selenium for JavaScript-heavy sites")
        
        # Test a few promising links
        print("\n🧪 Testing Promising Links:")
        test_links = [url for text, url in unique_links if 
                     any(keyword in text.lower() for keyword in 
                         ['accommodation', 'search', 'property', 'listing'])]
        
        for url in test_links[:3]:
            try:
                print(f"  Testing: {url}")
                test_response = requests.get(url, headers=headers, timeout=5)
                print(f"    Status: {test_response.status_code}")
            except Exception as e:
                print(f"    Error: {e}")
        
    except Exception as e:
        print(f"❌ Error exploring website: {e}")

if __name__ == '__main__':
    explore_student24()

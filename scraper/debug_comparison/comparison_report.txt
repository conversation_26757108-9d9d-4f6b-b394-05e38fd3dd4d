<PERSON><PERSON><PERSON> vs <PERSON>rowser HTML Comparison
==================================================

SCRAPER ANALYSIS:
  source: scraper
  title: Student24 - Find any res
  content_length: 2446
  html_length: 21376
  has_accommodations_container: True
  has_search_input: True
  accommodation_divs_count: 0
  accommodation_keywords: 5
  price_mentions: 10
  script_count: 13
  has_accommodation_js: False

BROWSER ANALYSIS:
  source: browser
  title: Student24 - Find any res
  content_length: 4699
  html_length: 34823
  has_accommodations_container: True
  has_search_input: True
  accommodation_divs_count: 8
  accommodation_keywords: 12
  price_mentions: 18
  script_count: 13
  has_accommodation_js: False

COMPARISON:
  source_differs: Scraper: scraper, Browser: browser
  title_same: True
  content_length_difference: -2253
  html_length_difference: -13447
  has_accommodations_container_difference: 0
  has_search_input_difference: 0
  accommodation_divs_count_difference: -8
  accommodation_keywords_difference: -7
  price_mentions_difference: -8
  script_count_difference: 0
  has_accommodation_js_difference: 0

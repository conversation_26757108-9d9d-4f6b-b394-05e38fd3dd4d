#!/usr/bin/env python3
"""
Test the enhanced Student24 search scraper
Uses the search functionality to find accommodations
"""

import sys
import os
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_search_scraper():
    """Test the enhanced search scraper"""
    
    print("🔍 Testing Enhanced Student24 Search Scraper")
    print("=" * 70)
    print("This scraper uses the search input to find accommodations by university")
    print()
    
    try:
        from scrapers.student24_search_scraper import Student24SearchScraper, UNIVERSITY_SEARCH_TERMS
        print("✅ Enhanced search scraper imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import search scraper: {e}")
        return False
    
    try:
        # Create output directory
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # Initialize scraper
        print("🚀 Initializing enhanced search scraper...")
        scraper = Student24SearchScraper(delay=2)
        
        # Show what universities we'll search for
        print(f"\n🎯 Will search for {len(UNIVERSITY_SEARCH_TERMS)} universities:")
        for i, term in enumerate(UNIVERSITY_SEARCH_TERMS[:8], 1):  # Show first 8
            print(f"  {i}. {term}")
        if len(UNIVERSITY_SEARCH_TERMS) > 8:
            print(f"  ... and {len(UNIVERSITY_SEARCH_TERMS) - 8} more")
        
        # Scrape accommodations
        print(f"\n📡 Starting search-based scraping...")
        print("This may take a few minutes as we search each university...")
        
        listings = scraper.scrape_accommodations()
        
        print(f"\n📊 SEARCH SCRAPING RESULTS:")
        print(f"Total accommodations found: {len(listings)}")
        
        if listings:
            print(f"\n🏠 ACCOMMODATION DETAILS:")
            print("=" * 70)
            
            for i, listing in enumerate(listings, 1):
                print(f"\n📋 ACCOMMODATION {i}:")
                print(f"  🏷️  Title: {listing.get('title', 'N/A')}")
                print(f"  💰 Price: R{listing.get('price', 0):,.0f} per month")
                print(f"  📍 Location: {listing.get('location', {}).get('name', 'N/A')}")
                
                # Show coordinates if available
                location = listing.get('location', {})
                if location.get('latitude'):
                    print(f"  🗺️  Coordinates: {location['latitude']:.4f}, {location['longitude']:.4f}")
                
                description = listing.get('description', '')
                if description:
                    print(f"  📝 Description: {description[:100]}...")
                
                features = listing.get('features', [])
                if features:
                    print(f"  🏷️  Features: {', '.join(features[:5])}")
                
                contact = listing.get('contact', {})
                print(f"  📞 Contact: {contact.get('type', 'N/A')} - {contact.get('value', 'N/A')[:50]}...")
                
                if listing.get('images'):
                    print(f"  🖼️  Images: {len(listing.get('images', []))} images available")
                
                print(f"  🔗 URL: {listing.get('url', 'N/A')}")
                print(f"  🕒 Scraped: {listing.get('scraped_at', 'N/A')}")
            
            # Save detailed results
            detailed_file = output_dir / "student24_search_detailed.json"
            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "accommodations": listings,
                    "metadata": {
                        "total_count": len(listings),
                        "scraper": "student24_search",
                        "search_method": "university_search",
                        "universities_searched": UNIVERSITY_SEARCH_TERMS
                    }
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Detailed results saved to: {detailed_file}")
            
            # Test data validation
            print(f"\n🔍 DATA VALIDATION:")
            try:
                from utils.data_processor import DataProcessor
                
                # Validate each listing
                valid_listings = []
                for listing in listings:
                    if DataProcessor.validate_listing(listing):
                        valid_listings.append(listing)
                        print(f"  ✅ {listing['title']}: Valid")
                    else:
                        print(f"  ❌ {listing['title']}: Invalid")
                
                print(f"\n📈 VALIDATION SUMMARY:")
                print(f"  Valid listings: {len(valid_listings)}/{len(listings)}")
                
                if valid_listings:
                    # Generate data quality report
                    report = DataProcessor.generate_report(valid_listings)
                    
                    print(f"\n📊 DATA QUALITY REPORT:")
                    print(f"  Sources: {report.get('sources', {})}")
                    print(f"  Data completeness:")
                    for key, value in report.get('data_completeness', {}).items():
                        print(f"    {key}: {value}")
                    
                    if report.get('price_statistics'):
                        stats = report['price_statistics']
                        print(f"  Price statistics:")
                        print(f"    Average: R{stats['average']:,.0f}")
                        print(f"    Range: R{stats['min']:,.0f} - R{stats['max']:,.0f}")
                    
                    # Save validated results
                    validated_file = output_dir / "student24_search_validated.json"
                    with open(validated_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            "accommodations": valid_listings,
                            "report": report,
                            "metadata": {
                                "total_count": len(valid_listings),
                                "validation_success_rate": f"{len(valid_listings)}/{len(listings)}",
                                "scraper": "student24_search"
                            }
                        }, f, indent=2, ensure_ascii=False)
                    
                    print(f"\n💾 Validated results saved to: {validated_file}")
                
            except Exception as e:
                print(f"⚠️  Data validation error: {e}")
            
            # Clean up
            scraper.close()
            
            return True
        
        else:
            print("❌ No accommodations found")
            print("\nPossible reasons:")
            print("1. Website search functionality has changed")
            print("2. No accommodations available for searched universities")
            print("3. Network connectivity issues")
            print("4. Website is blocking requests")
            
            print("\n🔧 Troubleshooting suggestions:")
            print("1. Check if https://student24.co is accessible")
            print("2. Try running with a longer delay: --delay 5.0")
            print("3. Check the logs for more detailed error information")
            
            scraper.close()
            return False
        
    except Exception as e:
        print(f"❌ Error testing search scraper: {e}")
        print(f"\n🔧 Error details: {type(e).__name__}: {e}")
        return False

def main():
    """Run the enhanced search scraper test"""
    
    print("🧪 Student24 Enhanced Search Scraper Test")
    print("=" * 70)
    
    success = test_search_scraper()
    
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    if success:
        print("🎉 SUCCESS! Enhanced search scraper is working")
        print("\nWhat we achieved:")
        print("✅ Used Student24.co search functionality")
        print("✅ Searched multiple universities")
        print("✅ Extracted detailed accommodation information")
        print("✅ Validated and processed data")
        print("✅ Generated comprehensive reports")
        
        print("\nData extracted includes:")
        print("📄 Accommodation titles and descriptions")
        print("💰 Pricing information")
        print("📍 University locations with coordinates")
        print("🏷️  Property features and amenities")
        print("📞 Contact information")
        print("🖼️  Property images")
        print("🔗 Direct links to accommodations")
        
        print("\nNext steps:")
        print("1. Use this scraper in production:")
        print("   python3 main.py --scraper search")
        print("2. Integrate with your application")
        print("3. Set up automated scraping schedule")
        
    else:
        print("❌ Test failed")
        print("\nTroubleshooting:")
        print("1. Check internet connectivity")
        print("2. Verify Student24.co is accessible")
        print("3. Check if website structure has changed")
        print("4. Try with different delay settings")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

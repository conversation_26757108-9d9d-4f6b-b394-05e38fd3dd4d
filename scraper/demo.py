#!/usr/bin/env python3
"""
Demo script to test the property scraper with minimal dependencies
"""

import os
import sys
import json
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_sample_data():
    """Create sample property listing data to demonstrate the system"""
    
    from utils.data_processor import DataProcessor
    from utils.geocoding import GeocodingService, get_fallback_coordinates
    
    print("🏠 Property Scraper Demo")
    print("=" * 50)
    
    # Test geocoding
    print("\n📍 Testing Geocoding:")
    geocoder = GeocodingService()
    
    test_areas = ["Hatfield", "Rondebosch", "Observatory", "Mowbray"]
    for area in test_areas:
        coords = geocoder.geocode_address(area)
        if coords:
            print(f"  ✅ {area}: {coords}")
        else:
            print(f"  ❌ {area}: No coordinates found")
    
    # Create sample listings
    print("\n📋 Creating Sample Listings:")
    
    sample_listings = [
        {
            "id": "demo_001",
            "source": "gumtree",
            "title": "2 Bedroom Student Flat - Hatfield",
            "price": 4500,
            "currency": "ZAR",
            "location": {
                "name": "Hatfield",
                "address": "123 University Road, Hatfield, Pretoria",
                "latitude": -25.7479,
                "longitude": 28.2293
            },
            "description": "Spacious 2 bedroom apartment perfect for students. Close to University of Pretoria. Features include 1 bathroom, parking, and WiFi.",
            "features": ["2 bedrooms", "1 bathroom", "parking", "wifi"],
            "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
            "contact": {
                "type": "phone",
                "value": "************"
            },
            "scraped_at": "2025-01-17T10:30:00Z",
            "url": "https://gumtree.co.za/demo-listing-1"
        },
        {
            "id": "demo_002", 
            "source": "property24",
            "title": "Student Accommodation near UCT - Rondebosch",
            "price": 3800,
            "currency": "ZAR",
            "location": {
                "name": "Rondebosch",
                "address": "456 Main Road, Rondebosch, Cape Town",
                "latitude": -33.9577,
                "longitude": 18.4739
            },
            "description": "Modern 1 bedroom unit in secure complex. Walking distance to UCT. Furnished with kitchen and bathroom.",
            "features": ["1 bedroom", "1 bathroom", "furnished", "security"],
            "images": ["https://example.com/image3.jpg"],
            "contact": {
                "type": "whatsapp",
                "value": "https://wa.me/27821234567"
            },
            "scraped_at": "2025-01-17T10:35:00Z",
            "url": "https://property24.com/demo-listing-2"
        },
        {
            "id": "demo_003",
            "source": "privateproperty", 
            "title": "Shared Student House - Observatory",
            "price": 2500,
            "currency": "ZAR",
            "location": {
                "name": "Observatory",
                "address": "789 Station Road, Observatory, Cape Town",
                "latitude": -33.9249,
                "longitude": 18.4732
            },
            "description": "Room in shared house with 3 other students. Includes kitchen access, garden, and parking.",
            "features": ["shared house", "garden", "parking", "kitchen"],
            "images": [],
            "contact": {
                "type": "email",
                "value": "<EMAIL>"
            },
            "scraped_at": "2025-01-17T10:40:00Z",
            "url": "https://privateproperty.co.za/demo-listing-3"
        }
    ]
    
    print(f"  ✅ Created {len(sample_listings)} sample listings")
    
    # Test data processing
    print("\n🔧 Testing Data Processing:")
    
    # Test validation
    valid_count = 0
    for listing in sample_listings:
        if DataProcessor.validate_listing(listing):
            valid_count += 1
    print(f"  ✅ Validation: {valid_count}/{len(sample_listings)} listings valid")
    
    # Test deduplication
    original_count = len(sample_listings)
    deduplicated = DataProcessor.deduplicate_listings(sample_listings)
    print(f"  ✅ Deduplication: {original_count} -> {len(deduplicated)} listings")
    
    # Test feature extraction
    test_desc = "Beautiful 3 bedroom house with 2 bathrooms, swimming pool, and secure parking"
    features = DataProcessor.extract_features(test_desc)
    print(f"  ✅ Feature extraction: {features}")
    
    # Test contact extraction
    test_contact = "Call John on 082 555 1234 or WhatsApp https://wa.me/27825551234"
    contact = DataProcessor.extract_contact_info(test_contact)
    print(f"  ✅ Contact extraction: {contact}")
    
    # Generate report
    print("\n📊 Generating Report:")
    report = DataProcessor.generate_report(sample_listings)
    
    print(f"  Total listings: {report['total_listings']}")
    print(f"  Sources: {report['sources']}")
    print(f"  Data completeness:")
    for key, value in report['data_completeness'].items():
        print(f"    {key}: {value}")
    
    if report.get('price_statistics'):
        stats = report['price_statistics']
        print(f"  Price range: R{stats['min']:,.0f} - R{stats['max']:,.0f}")
        print(f"  Average: R{stats['average']:,.0f}")
    
    # Save sample data
    print("\n💾 Saving Sample Data:")
    
    # Create output directory
    Path("output").mkdir(exist_ok=True)
    
    # Save as JSON
    json_file = "output/demo_listings.json"
    DataProcessor.export_to_json(sample_listings, json_file)
    print(f"  ✅ JSON saved: {json_file}")
    
    # Save as CSV
    csv_file = "output/demo_listings.csv"
    DataProcessor.export_to_csv(sample_listings, csv_file)
    print(f"  ✅ CSV saved: {csv_file}")
    
    # Save report
    report_file = "output/demo_report.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"  ✅ Report saved: {report_file}")
    
    print("\n🎉 Demo completed successfully!")
    print("\nFiles created:")
    print(f"  - {json_file}")
    print(f"  - {csv_file}")
    print(f"  - {report_file}")
    
    print("\nNext steps:")
    print("  1. Test import: python3 import_seed.py -f output/demo_listings.json -fmt json -t database")
    print("  2. Run real scraper: python3 main.py --sites gumtree --max-pages 1")
    
    return sample_listings

def main():
    """Run the demo"""
    try:
        create_sample_data()
        return True
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

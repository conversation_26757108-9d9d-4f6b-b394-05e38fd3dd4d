#!/usr/bin/env python3
"""
URL Discovery Tool for South African Property Websites
Helps find current working URLs for student accommodation
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
import time

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url(url, timeout=10):
    """Test if a URL is accessible and returns valid content"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout)
        
        if response.status_code == 200:
            # Check if it's a valid HTML page with content
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for signs this is a property listing page
            property_indicators = [
                'property', 'rental', 'rent', 'accommodation', 
                'listing', 'apartment', 'house', 'flat'
            ]
            
            page_text = soup.get_text().lower()
            has_property_content = any(indicator in page_text for indicator in property_indicators)
            
            return {
                'status': 'SUCCESS',
                'status_code': response.status_code,
                'has_property_content': has_property_content,
                'title': soup.title.string if soup.title else 'No title',
                'content_length': len(response.content)
            }
        else:
            return {
                'status': 'HTTP_ERROR',
                'status_code': response.status_code,
                'has_property_content': False
            }
            
    except requests.exceptions.Timeout:
        return {'status': 'TIMEOUT', 'status_code': None}
    except requests.exceptions.ConnectionError:
        return {'status': 'CONNECTION_ERROR', 'status_code': None}
    except Exception as e:
        return {'status': 'ERROR', 'error': str(e), 'status_code': None}

def discover_gumtree_urls():
    """Discover working Gumtree URLs"""
    print("🔍 Discovering Gumtree URLs...")
    
    base_urls = [
        "https://www.gumtree.co.za",
        "https://www.gumtree.co.za/s-property-for-rent/c9073",
        "https://www.gumtree.co.za/s-property-for-rent/gauteng/c9073l3100004",
        "https://www.gumtree.co.za/s-property-for-rent/western-cape/c9073l3100001",
        "https://www.gumtree.co.za/s-property-for-rent/pretoria/c9073l3100317",
        "https://www.gumtree.co.za/s-property-for-rent/cape-town/c9073l3100021",
    ]
    
    working_urls = []
    
    for url in base_urls:
        print(f"  Testing: {url}")
        result = test_url(url)
        
        if result['status'] == 'SUCCESS':
            print(f"    ✅ Working! ({result['status_code']}) - {result['title'][:50]}...")
            if result['has_property_content']:
                print(f"    🏠 Contains property content")
                working_urls.append(url)
            else:
                print(f"    ⚠️  No property content detected")
        else:
            print(f"    ❌ Failed: {result['status']} ({result.get('status_code', 'N/A')})")
        
        time.sleep(1)  # Be respectful
    
    return working_urls

def discover_property24_urls():
    """Discover working Property24 URLs"""
    print("\n🔍 Discovering Property24 URLs...")
    
    base_urls = [
        "https://www.property24.com",
        "https://www.property24.com/to-rent",
        "https://www.property24.com/to-rent/gauteng",
        "https://www.property24.com/to-rent/western-cape",
        "https://www.property24.com/to-rent/gauteng/pretoria",
        "https://www.property24.com/to-rent/western-cape/cape-town",
    ]
    
    working_urls = []
    
    for url in base_urls:
        print(f"  Testing: {url}")
        result = test_url(url)
        
        if result['status'] == 'SUCCESS':
            print(f"    ✅ Working! ({result['status_code']}) - {result['title'][:50]}...")
            if result['has_property_content']:
                print(f"    🏠 Contains property content")
                working_urls.append(url)
            else:
                print(f"    ⚠️  No property content detected")
        else:
            print(f"    ❌ Failed: {result['status']} ({result.get('status_code', 'N/A')})")
        
        time.sleep(1)  # Be respectful
    
    return working_urls

def discover_privateproperty_urls():
    """Discover working Private Property URLs"""
    print("\n🔍 Discovering Private Property URLs...")
    
    base_urls = [
        "https://www.privateproperty.co.za",
        "https://www.privateproperty.co.za/to-rent",
        "https://www.privateproperty.co.za/to-rent/gauteng",
        "https://www.privateproperty.co.za/to-rent/western-cape",
        "https://www.privateproperty.co.za/to-rent/gauteng/pretoria",
        "https://www.privateproperty.co.za/to-rent/western-cape/cape-town",
    ]
    
    working_urls = []
    
    for url in base_urls:
        print(f"  Testing: {url}")
        result = test_url(url)
        
        if result['status'] == 'SUCCESS':
            print(f"    ✅ Working! ({result['status_code']}) - {result['title'][:50]}...")
            if result['has_property_content']:
                print(f"    🏠 Contains property content")
                working_urls.append(url)
            else:
                print(f"    ⚠️  No property content detected")
        else:
            print(f"    ❌ Failed: {result['status']} ({result.get('status_code', 'N/A')})")
        
        time.sleep(1)  # Be respectful
    
    return working_urls

def main():
    """Main URL discovery function"""
    print("🌐 South African Property URL Discovery Tool")
    print("=" * 60)
    print("This tool helps find current working URLs for property websites")
    print("Note: Some sites may block automated requests")
    print()
    
    all_working_urls = {}
    
    # Test each site
    all_working_urls['gumtree'] = discover_gumtree_urls()
    all_working_urls['property24'] = discover_property24_urls()
    all_working_urls['privateproperty'] = discover_privateproperty_urls()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY OF WORKING URLs")
    print("=" * 60)
    
    for site, urls in all_working_urls.items():
        print(f"\n{site.upper()}:")
        if urls:
            for url in urls:
                print(f"  ✅ {url}")
        else:
            print(f"  ❌ No working URLs found")
    
    # Generate updated scraper code
    print("\n" + "=" * 60)
    print("🔧 SUGGESTED SCRAPER UPDATES")
    print("=" * 60)
    
    if all_working_urls['gumtree']:
        print(f"\nGUMTREE_URLS = [")
        for url in all_working_urls['gumtree']:
            print(f'    "{url}",')
        print(f"]")
    
    if all_working_urls['property24']:
        print(f"\nPROPERTY24_URLS = [")
        for url in all_working_urls['property24']:
            print(f'    "{url}",')
        print(f"]")
    
    if all_working_urls['privateproperty']:
        print(f"\nPRIVATEPROPERTY_URLS = [")
        for url in all_working_urls['privateproperty']:
            print(f'    "{url}",')
        print(f"]")
    
    print("\n💡 Next Steps:")
    print("1. Update the URLs in the scraper files with the working ones above")
    print("2. Test the scraper with: python3 main.py --sites gumtree --max-pages 1")
    print("3. Check if the scrapers can find and extract listings")
    print("4. Adjust the CSS selectors if needed for current website layouts")

if __name__ == '__main__':
    main()

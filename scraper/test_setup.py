#!/usr/bin/env python3
"""
Test script to validate scraper setup and basic functionality
"""

import os
import sys
import logging
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test core imports
        import requests
        import bs4
        import pandas
        import click
        from dotenv import load_dotenv
        print("✅ Core dependencies imported successfully")
        
        # Test scraper imports
        from scrapers.base_scraper import BaseScraper
        from scrapers.gumtree_scraper import GumtreeScraper
        from scrapers.property24_scraper import Property24Scraper
        from scrapers.privateproperty_scraper import PrivatePropertyScraper
        print("✅ Scraper modules imported successfully")
        
        # Test utility imports
        from utils.geocoding import GeocodingService
        from utils.data_processor import DataProcessor
        print("✅ Utility modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_environment():
    """Test environment configuration"""
    print("\nTesting environment...")

    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")

        from dotenv import load_dotenv
        load_dotenv()

        # Check scraping configuration
        delay = os.getenv('SCRAPING_DELAY', '3')
        retries = os.getenv('MAX_RETRIES', '3')

        print(f"✅ Scraping delay: {delay}s")
        print(f"✅ Max retries: {retries}")

    else:
        print("⚠️  .env file not found. Copy .env.example to .env for custom configuration")
        print("✅ Will use default settings")

    return True

def test_geocoding():
    """Test geocoding functionality"""
    print("\nTesting geocoding...")

    try:
        from utils.geocoding import GeocodingService, get_fallback_coordinates

        # Test fallback coordinates
        coords = get_fallback_coordinates("hatfield")
        if coords:
            print(f"✅ Fallback coordinates work: Hatfield -> {coords}")

        # Test geocoding service initialization
        geocoder = GeocodingService()
        print("✅ Geocoding service initialized")

        # Test with known student areas
        test_areas = ["Rondebosch", "Hatfield", "Observatory"]
        for area in test_areas:
            test_coords = geocoder.geocode_address(area)
            if test_coords:
                print(f"✅ {area} -> {test_coords}")
            else:
                print(f"⚠️  No coordinates for {area}")

        return True

    except Exception as e:
        print(f"❌ Geocoding test failed: {e}")
        return False

def test_data_processor():
    """Test data processing functionality"""
    print("\nTesting data processor...")
    
    try:
        from utils.data_processor import DataProcessor
        
        # Test price cleaning
        test_prices = ["R 3,500 per month", "R3500", "3500", "R 2 500 pm"]
        for price_text in test_prices:
            cleaned = DataProcessor.clean_price(price_text)
            print(f"  Price: '{price_text}' -> {cleaned}")
        
        # Test feature extraction
        test_desc = "2 bedroom apartment with 1 bathroom, parking available, furnished"
        features = DataProcessor.extract_features(test_desc)
        print(f"  Features extracted: {features}")
        
        # Test contact extraction
        test_contact = "Contact John on 082 123 4567 or WhatsApp https://wa.me/27821234567"
        contact = DataProcessor.extract_contact_info(test_contact)
        print(f"  Contact extracted: {contact}")
        
        print("✅ Data processor tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Data processor test failed: {e}")
        return False

def test_scraper_initialization():
    """Test scraper initialization"""
    print("\nTesting scraper initialization...")
    
    try:
        from scrapers.gumtree_scraper import GumtreeScraper
        from scrapers.property24_scraper import Property24Scraper
        from scrapers.privateproperty_scraper import PrivatePropertyScraper
        
        # Initialize scrapers
        gumtree = GumtreeScraper(delay=1, max_retries=1)
        property24 = Property24Scraper(delay=1, max_retries=1)
        privateproperty = PrivatePropertyScraper(delay=1, max_retries=1)
        
        print("✅ All scrapers initialized successfully")
        print(f"  Gumtree: {gumtree.source_name}")
        print(f"  Property24: {property24.source_name}")
        print(f"  Private Property: {privateproperty.source_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraper initialization failed: {e}")
        return False

def test_directories():
    """Test that required directories exist or can be created"""
    print("\nTesting directories...")
    
    directories = ['output', 'logs']
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"✅ Created directory: {dir_name}")
            except Exception as e:
                print(f"❌ Failed to create directory {dir_name}: {e}")
                return False
        else:
            print(f"✅ Directory exists: {dir_name}")
    
    return True

def main():
    """Run all tests"""
    print("🧪 Property Scraper Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_environment,
        test_directories,
        test_geocoding,
        test_data_processor,
        test_scraper_initialization,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The scraper is ready to use.")
        print("\nNext steps:")
        print("1. Run: python3 main.py --sites gumtree --max-pages 1")
        print("2. Check output/ directory for results")
        print("3. Use import_seed.py to process the data")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\nCommon fixes:")
        print("1. Install missing dependencies: pip3 install -r requirements.txt")
        print("2. Copy .env.example to .env for custom configuration")
        print("3. Check file permissions")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

"""
Student24.co South Africa property scraper for student accommodation
"""

import re
import logging
import sys
import os
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scrapers.base_scraper import BaseScraper
from utils.data_processor import DataProcessor
from utils.geocoding import GeocodingService, get_fallback_coordinates

logger = logging.getLogger(__name__)


class Student24Scraper(BaseScraper):
    """Scraper for Student24.co South Africa student accommodation listings"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.base_domain = "https://student24.co"
        self.source_name = "student24"
        self.geocoding_service = GeocodingService()
    
    def get_listing_urls(self, base_url: str, max_pages: int = 5) -> List[str]:
        """Extract listing URLs from Student24 pages - treat each URL as a potential listing"""
        listing_urls = []

        logger.info(f"Checking if URL is a direct accommodation listing: {base_url}")
        soup = self.get_page(base_url)

        if not soup:
            logger.warning(f"Failed to load page: {base_url}")
            return []

        # Check if this is already an accommodation page
        page_text = soup.get_text().lower()
        accommodation_indicators = [
            'accommodation', 'residence', 'student housing', 'room', 'apartment',
            'booking', 'available', 'price', 'per month'
        ]

        has_accommodation_content = any(indicator in page_text for indicator in accommodation_indicators)

        if has_accommodation_content:
            # This appears to be an individual accommodation page
            logger.info(f"URL appears to be an accommodation listing: {base_url}")
            listing_urls.append(base_url)
        else:
            # Try to find accommodation links on this page
            logger.info(f"Searching for accommodation links on: {base_url}")

            # Find accommodation-related links
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link.get('href')
                text = link.get_text().lower()

                if href and any(keyword in href.lower() or keyword in text for keyword in
                              ['accommodation', 'residence', 'property', 'room', 'apartment']):
                    full_url = urljoin(self.base_domain, href)
                    if full_url not in listing_urls:
                        listing_urls.append(full_url)

        logger.info(f"Total accommodation URLs found: {len(listing_urls)}")
        return listing_urls
    
    def scrape_listing(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape a single Student24 listing"""
        soup = self.get_page(url)
        if not soup:
            return None
        
        try:
            # Extract title - try multiple selectors
            title = None
            title_selectors = [
                'h1',
                '.title', '.property-title', '.listing-title',
                '.accommodation-title', '.residence-name',
                '[class*="title"]', '[class*="name"]'
            ]
            
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = DataProcessor.clean_text(title_elem.get_text())
                    if title and len(title) > 5:  # Valid title
                        break
            
            if not title:
                title = "Student Accommodation"
            
            # Extract price - try multiple patterns
            price = None
            price_patterns = [
                r'R\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
                r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:ZAR|R)',
                r'Price[:\s]*R?\s*(\d+(?:,\d{3})*)',
                r'(\d+(?:,\d{3})*)\s*per\s*month'
            ]
            
            # Look in common price containers
            price_containers = soup.find_all(['div', 'span', 'p'], 
                                           class_=re.compile(r'price|cost|rent|amount'))
            price_containers.extend(soup.find_all(string=re.compile(r'R\s*\d+|\d+\s*per\s*month')))
            
            for container in price_containers:
                text = container.get_text() if hasattr(container, 'get_text') else str(container)
                for pattern in price_patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        price_str = match.group(1).replace(',', '')
                        try:
                            price = float(price_str)
                            break
                        except ValueError:
                            continue
                if price:
                    break
            
            if not price:
                price = 0
            
            # Extract location
            location_name = ""
            location_selectors = [
                '.location', '.address', '.area', '.suburb',
                '[class*="location"]', '[class*="address"]'
            ]
            
            for selector in location_selectors:
                location_elem = soup.select_one(selector)
                if location_elem:
                    location_name = DataProcessor.clean_text(location_elem.get_text())
                    if location_name and len(location_name) > 3:
                        break
            
            # Extract description
            description = ""
            desc_selectors = [
                '.description', '.about', '.details', '.content',
                '[class*="description"]', '[class*="detail"]'
            ]
            
            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    description = DataProcessor.clean_text(desc_elem.get_text())
                    if description and len(description) > 20:
                        break
            
            # Extract features/amenities
            features = []
            
            # Look for amenities lists
            amenity_containers = soup.find_all(['ul', 'div'], 
                                             class_=re.compile(r'amenities|features|facilities|includes'))
            
            for container in amenity_containers:
                items = container.find_all(['li', 'span', 'div'])
                for item in items:
                    text = DataProcessor.clean_text(item.get_text())
                    if text and 3 < len(text) < 50:
                        features.append(text)
            
            # Also extract from description
            desc_features = DataProcessor.extract_features(description)
            features.extend(desc_features)
            features = list(set(features))  # Remove duplicates
            
            # Extract images
            images = []
            img_elements = soup.find_all('img')
            
            for img in img_elements:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy')
                if src:
                    # Make absolute URL
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = self.base_domain + src
                    
                    # Filter out logos, icons, etc.
                    if (src.startswith('http') and 
                        not any(skip in src.lower() for skip in ['logo', 'icon', 'avatar', 'button']) and
                        any(ext in src.lower() for ext in ['.jpg', '.jpeg', '.png', '.webp'])):
                        images.append(src)
            
            # Extract contact info
            contact_info = None
            
            # Look for contact sections
            contact_containers = soup.find_all(['div', 'section'], 
                                             class_=re.compile(r'contact|phone|email|whatsapp'))
            
            for container in contact_containers:
                # Look for phone numbers
                phone_links = container.find_all('a', href=re.compile(r'tel:'))
                if phone_links:
                    phone = phone_links[0].get('href').replace('tel:', '')
                    contact_info = {"type": "phone", "value": phone}
                    break
                
                # Look for email
                email_links = container.find_all('a', href=re.compile(r'mailto:'))
                if email_links:
                    email = email_links[0].get('href').replace('mailto:', '')
                    contact_info = {"type": "email", "value": email}
                    break
                
                # Look for WhatsApp
                whatsapp_links = container.find_all('a', href=re.compile(r'wa\.me|whatsapp'))
                if whatsapp_links:
                    whatsapp = whatsapp_links[0].get('href')
                    contact_info = {"type": "whatsapp", "value": whatsapp}
                    break
            
            # Fallback: extract from description or page text
            if not contact_info:
                contact_info = DataProcessor.extract_contact_info(description)
            
            if not contact_info:
                # Default to website contact
                contact_info = {"type": "website", "value": url}
            
            # Generate listing ID
            listing_id = self.generate_listing_id(self.source_name, title, str(price), url)
            
            # Geocode location
            coordinates = None
            if location_name:
                coordinates = self.geocoding_service.geocode_address(location_name)
                if not coordinates:
                    coordinates = get_fallback_coordinates(location_name)
            
            # Create location dict
            location_dict = {"name": location_name}
            if location_name:
                location_dict["address"] = location_name
            if coordinates:
                location_dict.update({
                    "latitude": coordinates[0],
                    "longitude": coordinates[1]
                })
            
            # Create listing
            listing = self.create_listing_dict(
                id=listing_id,
                source=self.source_name,
                title=title,
                price=price,
                location=location_dict,
                description=description,
                features=features[:10],  # Limit features
                images=images[:5],  # Limit to 5 images
                contact=contact_info,
                url=url
            )
            
            logger.debug(f"Successfully scraped: {title} - R{price}")
            return listing
            
        except Exception as e:
            logger.error(f"Error parsing Student24 listing {url}: {e}")
            return None


# URLs for Student24.co - Real working accommodation URLs
STUDENT24_URLS = [
    # University of Johannesburg (UJ) - working URL you found
    "https://student24.co/accommodation?ai=qnht100txslt224txuzt127",

    # Try similar patterns for other universities
    "https://student24.co/accommodation",
    "https://student24.co/accommodation?ai=uct",
    "https://student24.co/accommodation?ai=wits",
    "https://student24.co/accommodation?ai=up",
    "https://student24.co/accommodation?ai=stellenbosch",
    "https://student24.co/accommodation?ai=ukzn",

    # Main page as fallback
    "https://student24.co/",
]

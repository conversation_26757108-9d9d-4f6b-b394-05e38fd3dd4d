"""
Student24.co Hybrid scraper - works with or without Selenium
Falls back to requests+BeautifulSoup if Selenium is not available
"""

import re
import logging
import sys
import os
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import Selenium, fall back gracefully
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

import requests
from bs4 import BeautifulSoup
from utils.data_processor import DataProcessor
from utils.geocoding import GeocodingService, get_fallback_coordinates

logger = logging.getLogger(__name__)


class Student24HybridScraper:
    """Hybrid scraper that uses Selenium if available, otherwise falls back to requests"""
    
    def __init__(self, delay: float = 3, max_retries: int = 3, prefer_selenium: bool = True):
        self.delay = delay
        self.max_retries = max_retries
        self.prefer_selenium = prefer_selenium and SELENIUM_AVAILABLE
        self.source_name = "student24_hybrid"
        self.geocoding_service = GeocodingService()
        self.driver = None
        self.session = None
        
        # Initialize the appropriate scraping method
        if self.prefer_selenium:
            try:
                self._setup_selenium()
                self.use_selenium = True
                logger.info("Using Selenium for dynamic content scraping")
            except Exception as e:
                logger.warning(f"Selenium setup failed, falling back to requests: {e}")
                self.use_selenium = False
                self._setup_requests()
        else:
            self.use_selenium = False
            self._setup_requests()
    
    def _setup_selenium(self):
        """Setup Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium not available")
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(10)
    
    def _setup_requests(self):
        """Setup requests session"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        logger.info("Using requests+BeautifulSoup for scraping")
    
    def scrape_accommodations(self, urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape accommodations from multiple URLs"""
        all_listings = []
        
        try:
            for i, url in enumerate(urls):
                logger.info(f"Scraping URL {i+1}/{len(urls)}: {url}")
                
                try:
                    if self.use_selenium:
                        listings = self._scrape_with_selenium(url)
                    else:
                        listings = self._scrape_with_requests(url)
                    
                    all_listings.extend(listings)
                    logger.info(f"Found {len(listings)} accommodations on {url}")
                    
                    # Respectful delay
                    if i < len(urls) - 1:
                        time.sleep(self.delay)
                        
                except Exception as e:
                    logger.error(f"Error scraping {url}: {e}")
                    continue
        
        finally:
            self._cleanup()
        
        logger.info(f"Total accommodations scraped: {len(all_listings)}")
        return all_listings
    
    def _scrape_with_selenium(self, url: str) -> List[Dict[str, Any]]:
        """Scrape using Selenium with search functionality"""
        listings = []

        try:
            # Go to the main page first
            self.driver.get("https://student24.co/")
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(2)

            # Define search locations for different universities
            search_locations = [
                "Johannesburg",  # UJ, Wits
                "Cape Town",     # UCT
                "Rondebosch",    # UCT area
                "Pretoria",      # UP
                "Hatfield",      # UP area
                "Stellenbosch",  # Stellenbosch University
                "Braamfontein",  # Wits area
            ]

            for location in search_locations:
                try:
                    logger.info(f"Searching for accommodations in: {location}")

                    # Find the search input
                    search_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "location"))
                    )

                    # Clear and enter the location
                    search_input.clear()
                    search_input.send_keys(location)
                    time.sleep(1)

                    # Submit the search (try Enter key or look for search button)
                    search_input.send_keys("\n")
                    time.sleep(3)  # Wait for results to load

                    # Extract accommodations from search results
                    location_listings = self._extract_search_results(location)
                    listings.extend(location_listings)

                    logger.info(f"Found {len(location_listings)} accommodations in {location}")

                    # Go back to main page for next search
                    self.driver.get("https://student24.co/")
                    time.sleep(2)

                except Exception as e:
                    logger.warning(f"Error searching for {location}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Selenium scraping error: {e}")

        return listings
    
    def _scrape_with_requests(self, url: str) -> List[Dict[str, Any]]:
        """Scrape using requests + BeautifulSoup with search simulation"""
        listings = []

        try:
            # For requests mode, we'll try to simulate searches by using different URLs
            # or by posting to search endpoints if we can find them

            # First, get the main page to understand the structure
            response = self.session.get("https://student24.co/", timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for search form to understand how searches work
            search_form = soup.find('form')
            search_input = soup.find('input', {'id': 'location'})

            if search_form and search_input:
                logger.info("Found search form, attempting to simulate searches")

                # Define search locations
                search_locations = [
                    "Johannesburg", "Cape Town", "Rondebosch", "Pretoria",
                    "Hatfield", "Stellenbosch", "Braamfontein"
                ]

                for location in search_locations:
                    try:
                        location_listings = self._simulate_search_requests(location)
                        listings.extend(location_listings)
                        logger.info(f"Found {len(location_listings)} accommodations in {location}")
                        time.sleep(1)  # Be respectful
                    except Exception as e:
                        logger.warning(f"Error searching for {location}: {e}")
                        continue
            else:
                # Fallback: try the original URL approach
                if self._is_accommodation_page_requests(soup):
                    listing = self._extract_accommodation_requests(soup, url)
                    if listing:
                        listings.append(listing)

        except Exception as e:
            logger.error(f"Requests scraping error: {e}")

        return listings
    
    def _is_accommodation_page_selenium(self) -> bool:
        """Check if current page is accommodation page (Selenium)"""
        try:
            page_text = self.driver.page_source.lower()
            indicators = ['accommodation', 'residence', 'student housing', 'booking', 'per month']
            return sum(1 for indicator in indicators if indicator in page_text) >= 3
        except:
            return False
    
    def _is_accommodation_page_requests(self, soup: BeautifulSoup) -> bool:
        """Check if page is accommodation page (requests)"""
        try:
            page_text = soup.get_text().lower()
            indicators = ['accommodation', 'residence', 'student housing', 'booking', 'per month']
            return sum(1 for indicator in indicators if indicator in page_text) >= 3
        except:
            return False
    
    def _extract_accommodation_selenium(self, url: str) -> Optional[Dict[str, Any]]:
        """Extract accommodation details using Selenium"""
        try:
            # Extract title
            title = "Student Accommodation"
            try:
                title_elem = self.driver.find_element(By.TAG_NAME, "h1")
                title = DataProcessor.clean_text(title_elem.text)
            except:
                pass
            
            # Extract price
            price = 0
            page_text = self.driver.page_source
            price_match = re.search(r'R\s*(\d+(?:,\d{3})*)', page_text, re.IGNORECASE)
            if price_match:
                try:
                    price = float(price_match.group(1).replace(',', ''))
                except:
                    pass
            
            # Extract location
            location_name = self._extract_location_from_url(url)
            
            # Extract description
            description = ""
            try:
                desc_elements = self.driver.find_elements(By.TAG_NAME, "p")
                descriptions = [elem.text.strip() for elem in desc_elements if len(elem.text.strip()) > 20]
                description = ' '.join(descriptions[:2])
            except:
                pass
            
            return self._create_listing(title, price, location_name, description, url)
            
        except Exception as e:
            logger.error(f"Error extracting accommodation with Selenium: {e}")
            return None
    
    def _extract_accommodation_requests(self, soup: BeautifulSoup, url: str) -> Optional[Dict[str, Any]]:
        """Extract accommodation details using BeautifulSoup"""
        try:
            # Extract title
            title = "Student Accommodation"
            title_elem = soup.find('h1')
            if title_elem:
                title = DataProcessor.clean_text(title_elem.get_text())
            
            # Extract price
            price = 0
            page_text = soup.get_text()
            price_match = re.search(r'R\s*(\d+(?:,\d{3})*)', page_text, re.IGNORECASE)
            if price_match:
                try:
                    price = float(price_match.group(1).replace(',', ''))
                except:
                    pass
            
            # Extract location
            location_name = self._extract_location_from_url(url)
            
            # Extract description
            description = ""
            desc_elements = soup.find_all('p')
            descriptions = [DataProcessor.clean_text(elem.get_text()) 
                          for elem in desc_elements if len(elem.get_text().strip()) > 20]
            description = ' '.join(descriptions[:2])
            
            return self._create_listing(title, price, location_name, description, url)
            
        except Exception as e:
            logger.error(f"Error extracting accommodation with requests: {e}")
            return None
    
    def _extract_location_from_url(self, url: str) -> str:
        """Extract location based on URL parameters"""
        url_lower = url.lower()
        
        # Map URL parameters to locations
        location_map = {
            'qnht100txslt224txuzt127': 'Johannesburg',  # UJ
            'uct': 'Cape Town',
            'wits': 'Johannesburg', 
            'up': 'Pretoria',
            'stellenbosch': 'Stellenbosch',
            'ukzn': 'Pietermaritzburg'
        }
        
        for key, location in location_map.items():
            if key in url_lower:
                return location
        
        return "South Africa"
    
    def _create_listing(self, title: str, price: float, location_name: str, description: str, url: str) -> Dict[str, Any]:
        """Create a standardized listing dictionary"""
        # Generate listing ID
        import hashlib
        listing_id = hashlib.md5(f"{self.source_name}_{title}_{price}_{url}".encode()).hexdigest()[:12]
        
        # Get coordinates
        coordinates = self.geocoding_service.geocode_address(location_name)
        if not coordinates:
            coordinates = get_fallback_coordinates(location_name)
        
        # Create location dict
        location_dict = {"name": location_name}
        if coordinates:
            location_dict.update({
                "latitude": coordinates[0],
                "longitude": coordinates[1]
            })
        
        # Extract features from description
        features = DataProcessor.extract_features(description)
        features.extend(['student accommodation', 'university area'])
        
        # Extract contact info
        contact_info = DataProcessor.extract_contact_info(description)
        if not contact_info:
            contact_info = {"type": "website", "value": url}
        
        return {
            'id': listing_id,
            'source': self.source_name,
            'title': title,
            'price': price,
            'currency': 'ZAR',
            'location': location_dict,
            'description': description,
            'features': list(set(features))[:10],
            'images': [],  # Could be enhanced to extract images
            'contact': contact_info,
            'scraped_at': time.strftime('%Y-%m-%dT%H:%M:%SZ'),
            'url': url
        }
    
    def _cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
        
        if self.session:
            try:
                self.session.close()
            except:
                pass
            self.session = None


# URLs for Student24 hybrid scraping
STUDENT24_HYBRID_URLS = [
    # The working URL you found
    "https://student24.co/accommodation?ai=qnht100txslt224txuzt127",
    
    # Try other university patterns
    "https://student24.co/accommodation?ai=uct",
    "https://student24.co/accommodation?ai=wits",
    "https://student24.co/accommodation?ai=up",
    "https://student24.co/accommodation?ai=stellenbosch",
    
    # Main accommodation page
    "https://student24.co/accommodation",
]

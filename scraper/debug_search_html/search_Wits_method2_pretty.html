<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <meta content="#00ACEE" name="theme-color"/>
  <meta content="Student24 is your go-to platform connecting students with landlords, offering diverse housing options and reaching 1.5 million students monthly. We empower landlords with tools for growth while solving student housing shortages across Africa." name="description"/>
  <script type="application/ld+json">
   {
            "@context": "https://schema.org",
            "@type": "Platform",
            "name": "Student24",
            "url": "https://student24.co",
            "logo": "https://student24.co/resources/images/brand/student24-logo.webp",
            "description": "Connecting students with landlords, offering diverse housing options. We empower landlords with tools for growth while solving student housing shortages across Africa.",
            "address": {
                "@type": "PostalAddress",
                "addressCountry": "South Africa"
            }
        }
  </script>
  <meta content="Student24 - Find any res" property="og:title"/>
  <meta content="Student24 is your go-to platform connecting students with landlords, offering diverse housing options and reaching 1.5 million students monthly. We empower landlords with tools for growth while solving student housing shortages across Africa." property="og:description"/>
  <meta content="https://student24.co/resources/images/og-image.png" property="og:image"/>
  <link href="https://student24.co/" rel="canonical"/>
  <link href="resources/images/brand/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <title>
   Student24 - Find any res
  </title>
  <base href="https://student24.co/"/>
  <!-- Google tag (gtag.js) -->
  <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-F2S18PXL69">
  </script>
  <script>
   window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F2S18PXL69');
  </script>
  <meta content="6agk1skra3leik5pr4o5vrorldkvae" name="facebook-domain-verification">
   <link href="css/main.css" rel="stylesheet"/>
   <link href="css/index.css" rel="stylesheet"/>
   <link href="css/responsive/index.css" rel="stylesheet"/>
   <link href="components/header/header.css" rel="stylesheet"/>
   <link href="components/footer/footer.css" rel="stylesheet"/>
   <link href="components/form/form.css" rel="stylesheet"/>
   <link href="components/auth/auth.css?v=1.1" rel="stylesheet"/>
   <link href="components/alert/alert.css" rel="stylesheet"/>
   <link href="css/components/accommodation.css" rel="stylesheet"/>
   <script src="https://code.jquery.com/jquery-3.6.0.min.js">
   </script>
   <script src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js" type="course">
   </script>
   <script nocourse="" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js">
   </script>
  </meta>
 </head>
 <body>
  <input id="si" type="hidden" value=""/>
  <input id="li" type="hidden" value=""/>
  <div id="alertContainer">
  </div>
  <header class="overlaid">
   <div class="inner-container flex-between">
    <div class="logo" onclick="goTo('')">
     <img alt="Student24 logo" src="resources/images/brand/student24-logo.webp"/>
    </div>
    <div class="overlay hidden" id="navigationOverlay" onclick="toggleMobileNav(0)">
    </div>
    <div class="tabs flex-right" id="tabs">
     <nav class="flex-left">
      <a class="flex-left" href="about">
       About
      </a>
      <a class="flex-left" href="contact">
       Contact
      </a>
      <a class="flex-left" href="landlords">
       Landlords
      </a>
     </nav>
     <div class="actions flex-right">
      <button class="secondary-btn flex-center" onclick="display('show', 'registrationModal')">
       Register
      </button>
      <button class="primary-btn" onclick="display('show','loginModal')">
       Login
      </button>
     </div>
    </div>
    <button class="secondary-btn mobile-menu flex-center round" onclick="toggleMobileNav(1)">
     <ion-icon name="menu">
     </ion-icon>
    </button>
   </div>
   <script src="components/header/header.js">
   </script>
  </header>
  <section class="hero bg-image flex-center">
   <div class="inner-container content">
    <h1>
     Find any res.
    </h1>
    <p class="intro">
     Find accommodations close to campus at your budget.
    </p>
    <div class="search">
     <div class="search-bar flex-between" id="searchBar">
      <div class="icon round flex-center">
       <ion-icon name="location-outline">
       </ion-icon>
      </div>
      <input id="location" onfocus="maximizeInput()" placeholder="Name of the city or suburb" type="search"/>
      <div class="search-filters hidden flex-right" id="searchFilters">
       <div class="filter flex-between">
        <p>
         Rent
        </p>
        <select id="maxRent">
         <option value="">
          any
         </option>
         <option value="2000">
          max R2000
         </option>
         <option value="3000">
          max R3000
         </option>
         <option value="4000">
          max R4000
         </option>
         <option value="5000">
          max R5000
         </option>
         <option value="6000">
          max R6000
         </option>
         <option value="7000">
          max R7000
         </option>
         <option value="8000">
          max R8000
         </option>
         <option value="9000">
          max R9000
         </option>
         <option value="10000">
          max R10000
         </option>
        </select>
       </div>
       <div class="filter flex-between">
        <p>
         Property
        </p>
        <select id="propertyType">
         <option value="">
          any
         </option>
         <option value="House">
          house
         </option>
         <option value="Apartment">
          apartment
         </option>
         <option value="PBSA">
          PBSA
         </option>
        </select>
       </div>
       <div class="filter flex-between">
        <p>
         Funding
        </p>
        <select id="paymentMethod">
         <option value="">
          any
         </option>
         <option value="cash">
          cash
         </option>
         <option value="nsfas">
          NSFAS
         </option>
         <option value="bursary">
          bursary
         </option>
        </select>
       </div>
      </div>
      <button class="flex-center round primary-btn search-btn" id="searchBtn" onclick="searchAccommodations()">
       <ion-icon name="search">
       </ion-icon>
      </button>
     </div>
     <div class="mobile-search-btn">
      <button class="primary-btn" onclick="searchAccommodations()">
       Find res
      </button>
     </div>
    </div>
    <div class="pre-searches flex-left">
     <div class="pre-search flex-center" onclick="filterByUniversity('UJ')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UJ
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('WITS')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to Wits
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UP')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UP
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UKZN')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UKZN
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('DUT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to DUT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UCT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UCT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('TUT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to TUT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('CPUT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to CPUT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UFS')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UFS
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UMP')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UMP
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('SUN')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to SUN
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('NWU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to NWU
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('WSU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to WSU
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('NMU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to NMU
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UWC')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UWC
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('CUT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to CUT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UFH')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UFH
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('VUT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to VUT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('RU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to RU
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UL')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UL
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UNIZULU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UNIZULU
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('UNIVEN')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to UNIVEN
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('MUT')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to MUT
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('SMU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to SMU
      </p>
     </div>
     <div class="pre-search flex-center" onclick="filterByUniversity('SPU')">
      <ion-icon name="search">
      </ion-icon>
      <p>
       close to SPU
      </p>
     </div>
    </div>
   </div>
  </section>
  <section class="city-filters" id="cityFilters">
   <div class="inner-container flex-center cities">
    <div class="city johannesburg" onclick="filterByCity('johannesburg')">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <h1>
       Johannesburg
      </h1>
      <p>
       Find res in Jo'burg
      </p>
     </div>
    </div>
    <div class="city cape-town" onclick="filterByCity('cape town')">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <h1>
       Cape Town
      </h1>
      <p>
       Find res in Cape Town
      </p>
     </div>
    </div>
    <div class="city durban" onclick="filterByCity('durban')">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <h1>
       Durban
      </h1>
      <p>
       Accommodations Durban
      </p>
     </div>
    </div>
    <div class="city pretoria" onclick="filterByCity('pretoria')">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <h1>
       Pretoria
      </h1>
      <p>
       Find res in Pretoria
      </p>
     </div>
    </div>
    <div class="city bloemfontein" onclick="filterByCity('bloemfontein')">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <h1>
       Bloemfontein
      </h1>
      <p>
       Find res in Bloemfontein
      </p>
     </div>
    </div>
   </div>
  </section>
  <section class="listings">
   <div class="section-title">
    <h1 id="accommodationTitle">
     Student accommodations
    </h1>
   </div>
   <div class="inner-container flex-center" id="accommodations">
    <div class="skeleton">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <div class="ratings flex-left">
       <div class="stars">
        <span>
        </span>
       </div>
       <span>
       </span>
      </div>
      <h1>
       <span>
       </span>
      </h1>
      <div class="description">
       <span>
       </span>
       <span>
       </span>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
     </div>
    </div>
    <div class="skeleton">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <div class="ratings flex-left">
       <div class="stars">
        <span>
        </span>
       </div>
       <span>
       </span>
      </div>
      <h1>
       <span>
       </span>
      </h1>
      <div class="description">
       <span>
       </span>
       <span>
       </span>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
     </div>
    </div>
    <div class="skeleton">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <div class="ratings flex-left">
       <div class="stars">
        <span>
        </span>
       </div>
       <span>
       </span>
      </div>
      <h1>
       <span>
       </span>
      </h1>
      <div class="description">
       <span>
       </span>
       <span>
       </span>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
     </div>
    </div>
    <div class="skeleton">
     <div class="image bg-image placeholder-24">
     </div>
     <div class="content">
      <div class="ratings flex-left">
       <div class="stars">
        <span>
        </span>
       </div>
       <span>
       </span>
      </div>
      <h1>
       <span>
       </span>
      </h1>
      <div class="description">
       <span>
       </span>
       <span>
       </span>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
      <div class="info flex-left">
       <div class="icon round">
       </div>
       <span>
       </span>
      </div>
     </div>
    </div>
   </div>
   <div class="pagination flex-center" id="pagination">
   </div>
  </section>
  <div class="section invite-landlords">
   <div class="inner-container">
    <h1>
     List your student accommodation
    </h1>
    <p>
     Student24 generates over +60 000 Leads and over R 100 000 000 value in lease agreements.
    </p>
    <button class="primary-btn" onclick="goTo('landlords')">
     Learn more
    </button>
   </div>
  </div>
  <section class="modal auth-modal login-modal flex-center hidden" id="loginModal">
   <div class="overlay" onclick="display('hide', 'loginModal')">
   </div>
   <div class="modal-content flex-between">
    <button class="close-auth-modal round flex-center" onclick="display('hide', 'loginModal')" type="button">
     <ion-icon name="close">
     </ion-icon>
    </button>
    <div class="left flex-center" id="loginForm">
     <div class="content">
      <p class="saying">
       Welcome to Student24
      </p>
      <div class="heading">
       <h1>
        Login to your account
       </h1>
       <p>
        Enter your details to login to your student/landlord portal.
       </p>
      </div>
      <div class="nb flex-left hidden" id="nbLogin">
       <ion-icon name="alert-circle-outline">
       </ion-icon>
       <p id="nbLoginResponse">
        Incorrect email or password
       </p>
      </div>
      <div class="form">
       <div class="input-box flex-left low-margin" id="loginEmailBox">
        <div class="icon flex-right">
         <ion-icon name="mail-outline">
         </ion-icon>
        </div>
        <input class="label-movable" id="loginEmail" type="email"/>
        <p>
         Email address
        </p>
        <span id="loginEmailResponse">
        </span>
       </div>
       <div class="input-box flex-left low-margin" id="loginPasswordBox">
        <div class="icon flex-right">
         <ion-icon name="finger-print-outline">
         </ion-icon>
        </div>
        <input class="label-movable" id="loginPassword" type="password"/>
        <p>
         Password
        </p>
        <div class="icon eyes flex-center" onclick="togglePassword('loginPassword')">
         <ion-icon id="loginPasswordEye" name="eye-off-outline">
         </ion-icon>
        </div>
        <span id="loginPasswordResponse">
        </span>
       </div>
      </div>
      <div class="action">
       <button class="primary-btn" id="loginBtn" onclick="login()">
        Login
       </button>
      </div>
      <p class="more-info">
       No account?
       <span onclick="display('hide', 'loginModal'); display('show', 'registrationModal');">
        Create one
       </span>
       .
                Forgot password?
       <a href="auth/reset-password">
        Reset it
       </a>
       .
      </p>
     </div>
    </div>
    <div class="right bg-image placeholder-24" id="loginRight">
     <div class="video">
      <video autoplay="" loop="" muted="" src="resources/videos/typing.mp4" width="100%">
      </video>
     </div>
    </div>
   </div>
  </section>
  <section class="modal auth-modal registration-modal flex-center hidden" id="registrationModal">
   <div class="overlay" onclick="display('hide', 'registrationModal')">
   </div>
   <div class="modal-content flex-between">
    <button class="close-auth-modal round flex-center" onclick="display('hide', 'registrationModal')" type="button">
     <ion-icon name="close">
     </ion-icon>
    </button>
    <div class="left flex-center" id="registrationForm">
     <div class="content">
      <p class="saying">
       Welcome to Student24
      </p>
      <div class="heading">
       <h1>
        Create an account
       </h1>
       <p>
        Enter your details to create your landlord/student account.
       </p>
      </div>
      <div class="nb flex-left hidden" id="nbRegistration">
       <ion-icon name="alert-circle-outline">
       </ion-icon>
       <p id="nbRegistrationResponse">
        Failed to create account
       </p>
      </div>
      <div class="form">
       <input id="userType" type="hidden"/>
       <div class="double-input flex-between user-types">
        <label class="user-type flex-left" for="landlord" id="landlordBox">
         <div class="radio flex-right">
          <input id="landlord" name="userType" oninput="markUserType('landlord')" type="radio" value="landlord"/>
         </div>
         <p>
          Landlord
         </p>
         <div class="icon flex-right">
          <ion-icon name="checkmark-circle">
          </ion-icon>
         </div>
        </label>
        <label class="user-type flex-left" for="student" id="studentBox">
         <div class="radio flex-right">
          <input id="student" name="userType" oninput="markUserType('student')" type="radio" value="student"/>
         </div>
         <p>
          Student
         </p>
         <div class="icon flex-right">
          <ion-icon name="checkmark-circle">
          </ion-icon>
         </div>
        </label>
       </div>
       <div class="double-input flex-between">
        <div class="input-box flex-left" id="registrationEmailBox">
         <div class="icon flex-right">
          <ion-icon name="mail-outline">
          </ion-icon>
         </div>
         <input class="label-movable" id="registrationEmail" type="email"/>
         <p>
          Email address
         </p>
         <span id="registrationEmailResponse">
          We will send an OTP
         </span>
        </div>
        <div class="input-box flex-left hidden" id="whatsappNumberBox">
         <div class="icon flex-right">
          <ion-icon name="logo-whatsapp">
          </ion-icon>
         </div>
         <input class="label-movable" id="whatsappNumber" type="number"/>
         <p>
          WhatsApp number
         </p>
         <span id="whatsappNumberResponse">
          For processing bookings
         </span>
        </div>
       </div>
       <div class="double-input flex-between">
        <div class="input-box flex-left low-margin" id="registrationPasswordBox">
         <div class="icon flex-right">
          <ion-icon name="finger-print-outline">
          </ion-icon>
         </div>
         <input class="label-movable" id="registrationPassword" type="password"/>
         <p>
          Password
         </p>
         <div class="icon eyes flex-center" onclick="togglePassword('registrationPassword')">
          <ion-icon id="registrationPasswordEye" name="eye-off-outline">
          </ion-icon>
         </div>
         <span id="registrationPasswordResponse">
         </span>
        </div>
        <div class="input-box flex-left low-margin" id="registrationPassword2Box">
         <div class="icon flex-right">
          <ion-icon name="finger-print-outline">
          </ion-icon>
         </div>
         <input class="label-movable" id="registrationPassword2" onkeyup="comparePasswords()" type="password"/>
         <p>
          Repeat password
         </p>
         <div class="icon eyes flex-center" onclick="togglePassword('registrationPassword2')">
          <ion-icon id="registrationPassword2Eye" name="eye-off-outline">
          </ion-icon>
         </div>
         <span id="registrationPassword2Response">
         </span>
        </div>
       </div>
      </div>
      <div class="action">
       <button class="primary-btn" id="registerBtn" onclick="register()">
        Create account
       </button>
      </div>
      <p class="more-info">
       By registering you agree to our
       <a href="regulations">
        terms and conditions
       </a>
       and
       <a href="regulations?type=privacy-policy">
        privacy policy
       </a>
       .
       <br/>
       Already have an account?
       <span onclick="display('hide', 'registrationModal'); display('show', 'loginModal');">
        Login here
       </span>
       .
      </p>
     </div>
    </div>
    <div class="right bg-image placeholder-24" id="registrationRight">
     <div class="video">
      <video autoplay="" loop="" muted="" src="resources/videos/typing.mp4" width="100%">
      </video>
     </div>
    </div>
   </div>
  </section>
  <footer>
   <div class="inner-container flex-between">
    <div class="footer-group group1">
     <div class="brand">
      <img alt="logo" src="resources/images/brand/24.png"/>
     </div>
    </div>
    <div class="footer-group group2">
     <div class="heading">
      <h2>
       Quick actions
      </h2>
     </div>
     <ul>
      <li>
       <a href="landlords">
        Landlords page
       </a>
      </li>
      <li>
       <a href="about">
        About us
       </a>
      </li>
      <li>
       <a href="contact">
        Contact us
       </a>
      </li>
      <li>
       <p onclick="display('show', 'registrationModal')">
        Create an account
       </p>
      </li>
      <li>
       <p onclick="display('show','loginModal')">
        Sign in
       </p>
      </li>
     </ul>
    </div>
    <div class="footer-group group3">
     <div class="heading">
      <h2>
       Legal info
      </h2>
     </div>
     <ul>
      <li>
       <a href="regulations">
        Terms and conditions
       </a>
      </li>
      <li>
       <a href="regulations?type=privacy-policy">
        Privacy policy
       </a>
      </li>
     </ul>
    </div>
    <div class="footer-group group4">
     <div class="heading">
      <h2>
       Connect with us
      </h2>
     </div>
     <div class="social-links flex-left">
      <div class="social round flex-center" onclick="window.open('https://www.facebook.com/student24.co?mibextid=LQQJ4d', '_blank')" title="Facebook">
       <ion-icon name="logo-facebook">
       </ion-icon>
      </div>
      <div class="social round flex-center" onclick="window.open('https://www.instagram.com/student24.co?igsh=cDgyZm8ycjZ2NHpw', '_blank')" title="Instagram">
       <ion-icon name="logo-instagram">
       </ion-icon>
      </div>
      <!-- <div class="social round flex-center">
                <ion-icon name="logo-linkedin"></ion-icon>
            </div> -->
      <!-- <div class="social round flex-center">
                <ion-icon name="logo-tiktok"></ion-icon>
            </div> -->
      <div class="social round flex-center" onclick="window.open('https://wa.me/+27817177057', '_blank')" title="WhatsApp">
       <ion-icon name="logo-whatsapp">
       </ion-icon>
      </div>
     </div>
     <div class="subscribe-heading">
      <h2>
       Subscribe
      </h2>
      <p>
       Join our newsletter (Coming soon)
      </p>
     </div>
     <div class="subscribe flex-left">
      <input placeholder="Your email..." type="text"/>
      <button class="primary-btn">
       Subscribe
      </button>
     </div>
    </div>
   </div>
   <div class="copyright">
    <div class="inner-container flex-between">
     <p>
      2025 Student24. All Rights Reserved
     </p>
     <p onclick="window.open('https://www.thokozanikubheka.co.za', '_blank')" style="cursor: pointer">
      Powered by TK Web Solutions
     </p>
    </div>
   </div>
  </footer>
  <script src="js/main.js">
  </script>
  <script src="components/alert/alert.js">
  </script>
  <script src="components/form/form.js">
  </script>
  <script src="components/auth/registration/registration.js?v=1.1">
  </script>
  <script src="components/auth/login/login.js">
  </script>
  <script src="js/index.js">
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#00ACEE">
    <meta name="description" content="">
    
    <!-- open graph with school details -->
    <meta property="og:title" content=" - Student24">
    <meta property="og:description" content="">
    <meta property="og:image" content="https://student24.co/resources/images/accommodations/acc/">
    
    <link rel="shortcut icon" href="resources/images/brand/favicon.ico" type="image/x-icon">

    <title> - Student24</title>

    <base href='https://student24.co/'><!-- Google tag (gtag.js) -->
<script async src='https://www.googletagmanager.com/gtag/js?id=G-F2S18PXL69'></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F2S18PXL69');
</script>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/accommodation.css?v=1.1">
    <link rel="stylesheet" href="css/responsive/accommodation.css?v=1.1">
    <link rel="stylesheet" href="components/header/header.css">
    <link rel="stylesheet" href="components/footer/footer.css">
    <link rel="stylesheet" href="components/alert/alert.css">
    <link rel="stylesheet" href="components/form/form.css">
    <link rel="stylesheet" href="components/auth/auth.css?v=1.1">
    <link rel="stylesheet" href="css/components/accommodation.css">

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script type="course" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nocourse src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <script type="text/javascript">(function(){ emailjs.init("kUqSB5YqbdNB5Cgrk") })()</script>

</head>

<body>

    <input type="hidden" id="si" value="">
    <input type="hidden" id="li" value="">

    <div id="alertContainer"></div>

    <div class="modal flex-center hidden" id="loader">
        <div class="overlay"></div>
        <div class="loader flex-left">
            <div class="round"></div><p>Loading, please wait...</p>
        </div>
    </div>

    <header>

        
<div class="inner-container flex-between">
    <div class="logo" onclick="goTo('')">
        <img src="resources/images/brand/student24-logo.webp" alt="Student24 logo">
    </div>
    <div class="overlay hidden" onclick="toggleMobileNav(0)" id="navigationOverlay"></div>
    <div class="tabs flex-right" id="tabs">
        <nav class="flex-left">

            <a href="/" class="flex-left">Home</a>            <a href="about" class="flex-left ">About</a>
            <a href="contact" class="flex-left ">Contact</a>
            <a href="landlords" class="flex-left ">Landlords</a>

        </nav>

        
            <div class="actions flex-right">
                <button class="secondary-btn flex-center" onclick="display('show', 'registrationModal')">Register</button>
                <button class="primary-btn" onclick="display('show','loginModal')">Login</button>
            </div>

               
    </div>
    <button class="secondary-btn mobile-menu flex-center round" onclick="toggleMobileNav(1)">
        <ion-icon name="menu"></ion-icon>
    </button>
</div>

<script src="components/header/header.js"></script>
    </header>

    <div class="modal images-modal hidden" id="imageModal">

        <div class="overlay" onclick="closeModal()"></div>
        
        <div class="modal-content">

            <button class="close-modal round flex-center" onclick="closeModal()">
                <ion-icon name="close"></ion-icon>
            </button>
            
            <img id="modalImage" src="">

            <div class="arrows flex-between">

                <button class="flex-center round primary-btn" onclick="prevImage()">
                    <ion-icon name="chevron-back"></ion-icon>
                </button>

                <button class="flex-center round primary-btn" onclick="nextImage()">
                    <ion-icon name="chevron-forward"></ion-icon>
                </button>

            </div>
    
        </div>
    </div>

    <section class="gallery">

        <div class="inner-container flex-between">

            <div class="actions flex-left">
                <button class="flex-center round" id="likeBtn" onclick="likeAccommodation()">
                    <ion-icon name="heart-outline"></ion-icon>
                    <span id="likesCount" class="flex-center round"></span>
                </button>
                <button class="flex-center round" onclick="shareListing()">
                    <ion-icon name="share-social-outline"></ion-icon>
                </button>
            </div>
           
            <div class="left box bg-image placeholder-24" id="coverImage" onclick="showImage(0)">
                
            </div>

            <div class="right flex-between">

                <div class="top flex-between">

                    <div class="top box bg-image placeholder-24" id="image1" onclick="showImage(1)">
                        
                    </div>

                    <div class="top box bg-image placeholder-24" id="image2" onclick="showImage(2)">
                        
                    </div>

                </div>

                <div class="bottom flex-between">

                    <div class="box bg-image placeholder-24" id="image3" onclick="showImage(3)">
                        
                    </div>

                    <div class="box bg-image placeholder-24" id="image4" onclick="showImage(4)">
                        
                    </div>

                </div>

            </div>

            <button class="view-all-btn primary-btn" onclick="viewAllImages()">View all</button>
            
        </div>

    </section>

    <section class="res-info">

        <div class="inner-container flex-between">

            <div class="left">
                
                <div class="ratings flex-left" id="ratings">
                    <div class="stars flex-left">
                        <ion-icon name="star" class="dull"></ion-icon>
                        <ion-icon name="star" class="dull"></ion-icon>
                        <ion-icon name="star" class="dull"></ion-icon>
                        <ion-icon name="star" class="dull"></ion-icon>
                        <ion-icon name="star" class="dull"></ion-icon>
                    </div>
                    <p>0 (0 reviews)</p>
                </div>

                <h1 class="name" id="name">
                    Student accommodation name
                </h1>

                <div class="location-accreditation flex-left">

                    <div class="location flex-left">
                        <div class="icon round flex-center">
                            <ion-icon name="location-outline"></ion-icon>
                        </div>
                        <div>
                            <h1><span id="streetNumber">0</span> <span id="streetName">Street</span>, <span id="suburb">Suburb</span>, <span id="city">City</span></h1>
                            <p>Close to <span id="university">University</span> <span id="campus">Campus</span></p>
                        </div>
                    </div>

                    <div class="accreditation flex-left" id="accreditation">

                    </div>

                </div>

                <div class="description">
                    <p id="description">
                        Lorem, ipsum dolor sit amet consectetur adipisicing elit. Iste, iure laudantium! 
                        Necessitatibus culpa consectetur recusandae sint obcaecati distinctio autem voluptas odit 
                        numquam est mollitia suscipit libero, cupiditate cumque esse fugiat enim a earum excepturi. 
                        Sunt explicabo officia eius quas eum?
                    </p>
                    <button>Show full description</button>
                </div>

                <div class="features">

                    <div class="topic">
                        <h1>Features this place offers</h1>
                    </div>

                    <div class="list-features flex-left" id="features">

                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>
                        <div class="skeleton flex-left">
                            <span class="round"></span>
                            <span></span>
                        </div>

                    </div>

                    <button class="features-toggler-btn secondary-btn" id="featuresTogglerBtn" onclick="toggleFeatures()">
                        Show all features
                    </button>

                </div>

            </div>

            <div class="right">

                <div class="price-nsfas flex-between">

                    <div class="price">
                        <p>From</p>
                        <div class="flex-left">
                            <h1>R <span id="minimumRent">00</span></h1>
                            <p>per month</p>
                        </div>
                    </div>

                    <div class="nsfas yes round bg-image">
                        <div class="icon round flex-center" id="nsfasAccepted">
                            
                        </div>
                    </div>

                </div>

                <div class="deposit-topup flex-center">
                    <div class="box">
                        <p>Deposit</p>
                        <h1 id="deposit">R --</h1>
                    </div>
                    <div class="box">
                        <p>Top-up</p>
                        <h1 id="topup">R --</h1>
                    </div>
                </div>

                <div class="payment-method flex-center" id="paymentMethod">
                    
                </div>

                <div class="action">
                    <button class="primary-btn" onclick="scrollToView('rooms')">Book rooms</button>
                </div>

                <p class="notice">
                    It only takes seconds
                </p>

            </div>

        </div>

    </section>

    <div class="modal confirm-modal flex-center hidden" id="bookRoomModal">

        <div class="overlay" onclick="display('hide', 'bookRoomModal')"></div>

        <div class="content">
            <div class="top flex-between">
                <h1>Book room</h1>
                <button class="flex-center round" onclick="display('hide', 'bookRoomModal')">
                    <ion-icon name="close"></ion-icon>
                </button>
            </div>
            <input type="hidden" id="roomID">
            <p>The booking information will sent along with your first name, age, gender, university, campus, funding and funding status to the landlord.</p>
            <div class="actions flex-right">
                <button class="secondary-btn" onclick="display('hide', 'bookRoomModal')">
                    Cancel
                </button>
                <button class="primary-btn" onclick="bookRoom()">
                    Confirm
                </button>
            </div>

        </div>

    </div>

    <section class="rooms">
        <div class="inner-container">

            <div class="topic">
                <h1>Room types</h1>
            </div>

            <div class="types flex-left" id="rooms">

                <div class="skeleton flex-left">
                    <div class="image bg-image placeholder-24">
                        
                    </div>
                    <div class="content">
                        <h1 class="name"></h1>
                        <div class="unique flex-left">
                            <p></p>
                            <p></p>
                            <p></p>
                        </div>
                        <div class="price-book flex-between">
                            <h1 class="price"></h1>
                            <button></button>
                        </div>
                    </div>
                </div>

                <div class="skeleton flex-left">
                    <div class="image bg-image placeholder-24">
                        
                    </div>
                    <div class="content">
                        <h1 class="name"></h1>
                        <div class="unique flex-left">
                            <p></p>
                            <p></p>
                            <p></p>
                        </div>
                        <div class="price-book flex-between">
                            <h1 class="price"></h1>
                            <button></button>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </section>

    <section class="map">
        <div class="inner-container bg-image placeholder-24" id="map">

        </div>
    </section>

    <div class="reviews">

        <div class="inner-container">

            <div class="topic">
                <h1>Ratings and reviews</h1>
            </div>

            <div class="stats flex-between">

                <div class="left flex-left">
                    <div class="ratings">
                        <h1 id="rating">0</h1>
                        <div class="stars flex-left" id="stars">
                            <ion-icon name="star" class="dull"></ion-icon>
                            <ion-icon name="star" class="dull"></ion-icon>
                            <ion-icon name="star" class="dull"></ion-icon>
                            <ion-icon name="star" class="dull"></ion-icon>
                            <ion-icon name="star" class="dull"></ion-icon>
                        </div>
                    </div>
                </div>

                <div class="right">
                    <div class="row flex-between">
                        <p>5 (<span id="fivesCount">0</span>)</p>
                        <div class="progress">
                            <span id="fivesProgress"></span>
                        </div>
                    </div>
                    <div class="row flex-between">
                        <p>4 (<span id="foursCount">0</span>)</p>
                        <div class="progress">
                            <span id="foursProgress"></span>
                        </div>
                    </div>
                    <div class="row flex-between">
                        <p>3 (<span id="threesCount">0</span>)</p>
                        <div class="progress">
                            <span id="threesProgress"></span>
                        </div>
                    </div>
                    <div class="row flex-between">
                        <p>2 (<span id="twosCount">0</span>)</p>
                        <div class="progress">
                            <span id="twosProgress"></span>
                        </div>
                    </div>
                    <div class="row flex-between">
                        <p>1 (<span id="onesCount">0</span>)</p>
                        <div class="progress">
                            <span id="onesProgress"></span>
                        </div>
                    </div>
                </div>

                <div class="write-review flex-center">
                    <button class="primary-btn" onclick="display('show', 'writeReviewModal')">
                        Write a review
                    </button>
                </div>
    
            </div>

            <div class="modal flex-center write-review-modal hidden" id="writeReviewModal">

                <div class="overlay" onclick="display('hide', 'writeReviewModal')"></div>
        
                <div class="content form">
                
                    <div class="top flex-between">
                        <h1>Rate accommodation</h1>
                        <button class="flex-center round" onclick="display('hide', 'writeReviewModal')">
                            <ion-icon name="close"></ion-icon>
                        </button>
                    </div>
        
                    <div class="nb flex-left" id="nb">
                        <ion-icon name="alert-circle-outline"></ion-icon>
                        <p id="nbReviews">Have you stayed, or are you staying in this accommodation? Please tell us about it.</p>
                    </div>
        
                    <input type="hidden" id="starsCount">
        
                    <div class="rating flex-between">
                        <span class="rating-star" onclick="setRating(1)">&#9733;</span>
                        <span class="rating-star" onclick="setRating(2)">&#9733;</span>
                        <span class="rating-star" onclick="setRating(3)">&#9733;</span>
                        <span class="rating-star" onclick="setRating(4)">&#9733;</span>
                        <span class="rating-star" onclick="setRating(5)">&#9733;</span>
                    </div>
        
                    <div class="input-box label-up" id="reviewBox">
                        <textarea class="label-movable" id="review"></textarea>
                        <p id="reviewLabel">Review</p>
                        <span id="reviewResponse">Remember to be respectful and truthful</span>
                    </div>
        
                    <div class="submit-btn">
                        <button class="primary-btn" onclick="submitReview()">Submit</button>
                    </div>
        
                </div>

            </div>

            <div class="list-reviews flex-left" id="reviews">

                <div class="spinner round"></div>

            </div>

        </div>

    </div>

    <section class="listings" id="nearbyAccommodationsContainer">

        <div class="inner-container">

            <div class="topic">
                <h1>Nearby accommodations</h1>
            </div>

            <div class="accommodations flex-left" id="accommodations">

                <div class="skeleton">
                    <div class="image bg-image placeholder-24">
                    </div>
                    <div class="content">
                        <div class="ratings flex-left">
                            <div class="stars">
                                <span></span>
                            </div>
                            <span></span>
                        </div>
                        <h1><span></span></h1>
                        <div class="description">
                            <span></span><span></span><span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                    </div>
                </div>

                <div class="skeleton">
                    <div class="image bg-image placeholder-24">
                    </div>
                    <div class="content">
                        <div class="ratings flex-left">
                            <div class="stars">
                                <span></span>
                            </div>
                            <span></span>
                        </div>
                        <h1><span></span></h1>
                        <div class="description">
                            <span></span><span></span><span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                    </div>
                </div>

                <div class="skeleton">
                    <div class="image bg-image placeholder-24">
                    </div>
                    <div class="content">
                        <div class="ratings flex-left">
                            <div class="stars">
                                <span></span>
                            </div>
                            <span></span>
                        </div>
                        <h1><span></span></h1>
                        <div class="description">
                            <span></span><span></span><span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                    </div>
                </div>

                <div class="skeleton">
                    <div class="image bg-image placeholder-24">
                    </div>
                    <div class="content">
                        <div class="ratings flex-left">
                            <div class="stars">
                                <span></span>
                            </div>
                            <span></span>
                        </div>
                        <h1><span></span></h1>
                        <div class="description">
                            <span></span><span></span><span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                    </div>
                </div>

                <div class="skeleton">
                    <div class="image bg-image placeholder-24">
                    </div>
                    <div class="content">
                        <div class="ratings flex-left">
                            <div class="stars">
                                <span></span>
                            </div>
                            <span></span>
                        </div>
                        <h1><span></span></h1>
                        <div class="description">
                            <span></span><span></span><span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                        <div class="info flex-left">
                            <div class="icon round"></div>
                            <span></span>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </section>

    <section class="modal auth-modal login-modal flex-center hidden" id="loginModal">

        
<div class="overlay" onclick="display('hide', 'loginModal')"></div>

<div class="modal-content flex-between">

    <button class="close-auth-modal round flex-center" type="button" onclick="display('hide', 'loginModal')">
        <ion-icon name="close"></ion-icon>
    </button>

    <div class="left flex-center" id="loginForm">

        <div class="content">

            <p class="saying">Welcome to Student24</p>

            <div class="heading">
                <h1>Login to your account</h1>
                <p>Enter your details to login to your student/landlord portal.</p>
            </div>

            <div class="nb flex-left hidden" id="nbLogin">
                <ion-icon name="alert-circle-outline"></ion-icon>
                <p id="nbLoginResponse">Incorrect email or password</p>
            </div>

            <div class="form">

                <div class="input-box flex-left low-margin" id="loginEmailBox">
                    <div class="icon flex-right">
                        <ion-icon name="mail-outline"></ion-icon>
                    </div>
                    <input type="email" id="loginEmail" class="label-movable">
                    <p>Email address</p>
                    <span id="loginEmailResponse"></span>
                </div>
        
                <div class="input-box flex-left low-margin" id="loginPasswordBox">
                    <div class="icon flex-right">
                        <ion-icon name="finger-print-outline"></ion-icon>
                    </div>
                    <input type="password" id="loginPassword" class="label-movable">
                    <p>Password</p>
                    <div class="icon eyes flex-center" onclick="togglePassword('loginPassword')">
                        <ion-icon id="loginPasswordEye" name="eye-off-outline"></ion-icon>
                    </div>
                    <span id="loginPasswordResponse"></span>
                </div>

            </div>

            <div class="action">

                <button class="primary-btn" id="loginBtn" onclick="login()">
                    Login
                </button>

            </div>

            <p class="more-info">
                No account? <span onclick="display('hide', 'loginModal'); display('show', 'registrationModal');">Create one</span>.
                Forgot password? <a href="auth/reset-password">Reset it</a>.
            </p>

        </div>

    </div>

    <div class="right bg-image placeholder-24" id="loginRight">
        <div class="video">
            <video width="100%" autoplay muted loop src="resources/videos/typing.mp4"></video>
        </div>
    </div>

</div>

    </section>

    <section class="modal auth-modal registration-modal flex-center hidden" id="registrationModal">

        
<div class="overlay" onclick="display('hide', 'registrationModal')"></div>

<div class="modal-content flex-between">

    <button class="close-auth-modal round flex-center" type="button" onclick="display('hide', 'registrationModal')">
        <ion-icon name="close"></ion-icon>
    </button>

    <div class="left flex-center" id="registrationForm">

        <div class="content">

            <p class="saying">Welcome to Student24</p>

            <div class="heading">
                <h1>Create an account</h1>
                <p>Enter your details to create your landlord/student account.</p>
            </div>

            <div class="nb flex-left hidden" id="nbRegistration">
                <ion-icon name="alert-circle-outline"></ion-icon>
                <p id="nbRegistrationResponse">Failed to create account</p>
            </div>

            <div class="form">

                <input type="hidden" id="userType">

                <div class="double-input flex-between user-types">
        
                    <label for="landlord" class="user-type flex-left" id="landlordBox">
                        <div class="radio flex-right">
                            <input type="radio" name="userType" id="landlord" value="landlord" oninput="markUserType('landlord')">
                        </div>
                        <p>Landlord</p>
                        <div class="icon flex-right">
                            <ion-icon name="checkmark-circle"></ion-icon>
                        </div>
                    </label>

                    <label for="student" class="user-type flex-left" id="studentBox">
                        <div class="radio flex-right">
                            <input type="radio" name="userType" id="student" value="student" oninput="markUserType('student')">
                        </div>
                        <p>Student</p>
                        <div class="icon flex-right">
                            <ion-icon name="checkmark-circle"></ion-icon>
                        </div>
                    </label>

                </div>

                <div class="double-input flex-between">

                    <div class="input-box flex-left" id="registrationEmailBox">
                        <div class="icon flex-right">
                            <ion-icon name="mail-outline"></ion-icon>
                        </div>
                        <input type="email" id="registrationEmail" class="label-movable">
                        <p>Email address</p>
                        <span id="registrationEmailResponse">We will send an OTP</span>
                    </div>

                    <div class="input-box flex-left hidden" id="whatsappNumberBox">
                        <div class="icon flex-right">
                            <ion-icon name="logo-whatsapp"></ion-icon>
                        </div>
                        <input type="number" id="whatsappNumber" class="label-movable">
                        <p>WhatsApp number</p>
                        <span id="whatsappNumberResponse">For processing bookings</span>
                    </div>

                </div>

                <div class="double-input flex-between">
        
                    <div class="input-box flex-left low-margin" id="registrationPasswordBox">
                        <div class="icon flex-right">
                            <ion-icon name="finger-print-outline"></ion-icon>
                        </div>
                        <input type="password" id="registrationPassword" class="label-movable">
                        <p>Password</p>
                        <div class="icon eyes flex-center" onclick="togglePassword('registrationPassword')">
                            <ion-icon id="registrationPasswordEye" name="eye-off-outline"></ion-icon>
                        </div>
                        <span id="registrationPasswordResponse"></span>
                    </div>

                    <div class="input-box flex-left low-margin" id="registrationPassword2Box">
                        <div class="icon flex-right">
                            <ion-icon name="finger-print-outline"></ion-icon>
                        </div>
                        <input type="password" id="registrationPassword2" class="label-movable" onkeyup="comparePasswords()">
                        <p>Repeat password</p>
                        <div class="icon eyes flex-center" onclick="togglePassword('registrationPassword2')">
                            <ion-icon id="registrationPassword2Eye" name="eye-off-outline"></ion-icon>
                        </div>
                        <span id="registrationPassword2Response"></span>
                    </div>

                </div>

            </div>

            <div class="action">

                <button class="primary-btn" onclick="register()" id="registerBtn">
                    Create account
                </button>

            </div>

            <p class="more-info">
                By registering you agree to our <a href="regulations">terms and conditions</a> and <a href="regulations?type=privacy-policy">privacy policy</a>.
                <br>
                Already have an account? <span onclick="display('hide', 'registrationModal'); display('show', 'loginModal');">Login here</span>.
            </p>

        </div>

    </div>
    
    <div class="right bg-image placeholder-24" id="registrationRight">
        <div class="video">
            <video width="100%" autoplay muted loop src="resources/videos/typing.mp4"></video>
        </div>
    </div>

</div>
    </section>
    
    <section class="modal auth-modal booking-modal flex-center hidden" id="bookingFormModal">

        
<div class="overlay" onclick="display('hide', 'bookingFormModal')"></div>

<div class="modal-content flex-between">

    <button class="close-auth-modal round flex-center" type="button" onclick="display('hide', 'bookingFormModal')">
        <ion-icon name="close"></ion-icon>
    </button>

    <div class="left flex-center">

        <div class="content">

            <div class="heading">
                <h1>Book room</h1>
                <p>
                    To finalize your booking, we'll connect you with the landlord via WhatsApp. 
                    But first, please provide us your details so we can assist you.
                </p>
            </div>

            <div class="form">

                <input type="hidden" id="roomBookedID">

                <div class="double-input flex-between">
        
                    <div class="input-box flex-left without-icon low-margin" id="firstnameBox">
                        <input type="text" id="firstname" class="label-movable" oninput="moveLabelUp('firstname')">
                        <p>First name</p>
                    </div>

                    <div class="input-box flex-left without-icon low-margin" id="lastnameBox">
                        <input type="text" id="lastname" class="label-movable" oninput="moveLabelUp('lastname')">
                        <p>Surname</p>
                    </div>

                </div>

                <div class="input-box flex-left without-icon low-margin" id="studentEmailBox">
                    <input type="email" id="studentEmail" class="label-movable" oninput="moveLabelUp('studentEmail')">
                    <p>Email address</p>
                </div>

                <div class="double-input flex-between">
        
                    <div class="input-box flex-left without-icon" id="studentPhoneNumberBox">
                        <input type="number" id="studentPhoneNumber" class="label-movable" oninput="moveLabelUp('studentPhoneNumber')">
                        <p>Phone number</p>
                    </div>

                    <div class="input-box flex-left without-icon" id="studentNumberBox">
                        <input type="number" id="studentNumber" class="label-movable" oninput="moveLabelUp('studentNumber')">
                        <p>Student number</p>
                    </div>

                </div>

            </div>

            <div class="action">

                <button class="primary-btn" onclick="bookRoom()" id="bookBtn">
                    Continue
                </button>

            </div>

        </div>

    </div>
    
    <div class="right bg-image placeholder-24" id="roomBookedCoverImage">

    </div>

</div>
    </section>

    <footer>

        
<div class="inner-container flex-between">

    <div class="footer-group group1">

        <div class="brand">
            <img src="resources/images/brand/24.png" alt="logo">
        </div>

    </div>

    <div class="footer-group group2">

        <div class="heading">
            <h2>Quick actions</h2>
        </div>
        <ul>
            <li><a href="landlords">Landlords page</a></li>
            <li><a href="about">About us</a></li>
            <li><a href="contact">Contact us</a></li>
            <li><p onclick="display('show', 'registrationModal')">Create an account</p></li>
            <li><p onclick="display('show','loginModal')">Sign in</p></li>
        </ul>

    </div>

    <div class="footer-group group3">

        <div class="heading">
            <h2>Legal info</h2>
        </div>
        <ul>
            <li><a href="regulations">Terms and conditions</a></li>
            <li><a href="regulations?type=privacy-policy">Privacy policy</a></li>
        </ul>

    </div>

    <div class="footer-group group4">

        <div class="heading">
            <h2>Connect with us</h2>
        </div>
        
        <div class="social-links flex-left">

            <div class="social round flex-center" title="Facebook" onclick="window.open('https://www.facebook.com/student24.co?mibextid=LQQJ4d', '_blank')">
                <ion-icon name="logo-facebook"></ion-icon>
            </div>
            <div class="social round flex-center" title="Instagram" onclick="window.open('https://www.instagram.com/student24.co?igsh=cDgyZm8ycjZ2NHpw', '_blank')">
                <ion-icon name="logo-instagram"></ion-icon>
            </div>
            <!-- <div class="social round flex-center">
                <ion-icon name="logo-linkedin"></ion-icon>
            </div> -->
            <!-- <div class="social round flex-center">
                <ion-icon name="logo-tiktok"></ion-icon>
            </div> -->
            <div class="social round flex-center" title="WhatsApp" onclick="window.open('https://wa.me/+27817177057', '_blank')">
                <ion-icon name="logo-whatsapp"></ion-icon>
            </div>

        </div>

        <div class="subscribe-heading">
            <h2>Subscribe</h2>
            <p>Join our newsletter (Coming soon)</p>
        </div>

        <div class="subscribe flex-left">
            <input type="text" placeholder="Your email...">
            <button class="primary-btn">Subscribe</button>
        </div>

    </div>

</div>

<div class="copyright">
    <div class="inner-container flex-between">
        <p>2025 Student24. All Rights Reserved</p>
        <p style="cursor: pointer" onclick="window.open('https://www.thokozanikubheka.co.za', '_blank')">Powered by TK Web Solutions</p>
    </div>
</div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/data_files/features.js"></script>
    <script src="components/alert/alert.js"></script>
    <script src="components/form/form.js"></script>
    <script src="components/auth/registration/registration.js?v=1.1"></script>
    <script src="components/auth/login/login.js"></script>
    <script src="js/accommodation.js?v=1.1"></script>

</body>
</html>
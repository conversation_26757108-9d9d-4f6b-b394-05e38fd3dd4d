<html lang="en" class="hydrated"><head>
    <meta charset="UTF-8"><style data-styles="">ion-icon{visibility:hidden}.hydrated{visibility:inherit}</style>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#00ACEE">
    <meta name="description" content="Student24 is your go-to platform connecting students with landlords, offering diverse housing options and reaching 1.5 million students monthly. We empower landlords with tools for growth while solving student housing shortages across Africa.">
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Platform",
            "name": "Student24",
            "url": "https://student24.co",
            "logo": "https://student24.co/resources/images/brand/student24-logo.webp",
            "description": "Connecting students with landlords, offering diverse housing options. We empower landlords with tools for growth while solving student housing shortages across Africa.",
            "address": {
                "@type": "PostalAddress",
                "addressCountry": "South Africa"
            }
        }
    </script>
    <meta property="og:title" content="Student24 - Find any res">
    <meta property="og:description" content="Student24 is your go-to platform connecting students with landlords, offering diverse housing options and reaching 1.5 million students monthly. We empower landlords with tools for growth while solving student housing shortages across Africa.">
    <meta property="og:image" content="https://student24.co/resources/images/og-image.png">
    
    <link rel="canonical" href="https://student24.co/">
    <link rel="shortcut icon" href="resources/images/brand/favicon.ico" type="image/x-icon">

    <title>Student24 - Find any res</title>

    <base href="https://student24.co/"><!-- Google tag (gtag.js) -->
<script async="" src="https://www.googletagmanager.com/gtag/js?id=G-F2S18PXL69"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-F2S18PXL69');
</script>
    <meta name="facebook-domain-verification" content="6agk1skra3leik5pr4o5vrorldkvae">

    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/responsive/index.css">
    <link rel="stylesheet" href="components/header/header.css">
    <link rel="stylesheet" href="components/footer/footer.css">
    <link rel="stylesheet" href="components/form/form.css">
    <link rel="stylesheet" href="components/auth/auth.css?v=1.1">
    <link rel="stylesheet" href="components/alert/alert.css">
    <link rel="stylesheet" href="css/components/accommodation.css">

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script type="course" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nocourse="" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

</head>

<body>

    <input type="hidden" id="si" value="">
    <input type="hidden" id="li" value="">

    <div id="alertContainer"></div>

    <header class="overlaid">

        
<div class="inner-container flex-between">
    <div class="logo" onclick="goTo('')">
        <img src="resources/images/brand/student24-logo.webp" alt="Student24 logo">
    </div>
    <div class="overlay hidden" onclick="toggleMobileNav(0)" id="navigationOverlay"></div>
    <div class="tabs flex-right" id="tabs">
        <nav class="flex-left">

                        <a href="about" class="flex-left ">About</a>
            <a href="contact" class="flex-left ">Contact</a>
            <a href="landlords" class="flex-left ">Landlords</a>

        </nav>

        
            <div class="actions flex-right">
                <button class="secondary-btn flex-center" onclick="display('show', 'registrationModal')">Register</button>
                <button class="primary-btn" onclick="display('show','loginModal')">Login</button>
            </div>

               
    </div>
    <button class="secondary-btn mobile-menu flex-center round" onclick="toggleMobileNav(1)">
        <ion-icon name="menu" role="img" class="md hydrated"></ion-icon>
    </button>
</div>

<script src="components/header/header.js"></script>
    </header>

    <section class="hero bg-image flex-center">

        <div class="inner-container content">

            <h1>Find any res.</h1>
            <p class="intro">Find accommodations close to campus at your budget.</p>

            <div class="search">

                <div class="search-bar flex-between" id="searchBar" style="max-width: 1100px;">
                    <div class="icon round flex-center">
                        <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                    <input type="search" id="location" placeholder="Name of the city or suburb" onfocus="maximizeInput()">
                    <div class="search-filters flex-right displayed" id="searchFilters">
        
                        <div class="filter flex-between">
                            <p>Rent</p>
                            <select id="maxRent">
                                <option value="">any</option>
                                <option value="2000">max R2000</option>
                                <option value="3000">max R3000</option>
                                <option value="4000">max R4000</option>
                                <option value="5000">max R5000</option>
                                <option value="6000">max R6000</option>
                                <option value="7000">max R7000</option>
                                <option value="8000">max R8000</option>
                                <option value="9000">max R9000</option>
                                <option value="10000">max R10000</option>
                            </select>
                        </div>
        
                        <div class="filter flex-between">
                            <p>Property</p>
                            <select id="propertyType">
                                <option value="">any</option>
                                <option value="House">house</option>
                                <option value="Apartment">apartment</option>
                                <option value="PBSA">PBSA</option>
                            </select>
                        </div>
        
                        <div class="filter flex-between">
                            <p>Funding</p>
                            <select id="paymentMethod">
                                <option value="">any</option>
                                <option value="cash">cash</option>
                                <option value="nsfas">NSFAS</option>
                                <option value="bursary">bursary</option>
                            </select>
                        </div>
                    
                    </div>
                    <button class="flex-center round primary-btn search-btn" id="searchBtn" onclick="searchAccommodations()">
                        <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    </button>        
                </div>

                <div class="mobile-search-btn">
                    <button class="primary-btn" onclick="searchAccommodations()">Find res</button>
                </div>
        
            </div>

            <div class="pre-searches flex-left">
                <div class="pre-search flex-center" onclick="filterByUniversity('UJ')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UJ</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('WITS')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to Wits</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UP')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UP</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UKZN')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UKZN</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('DUT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to DUT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UCT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UCT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('TUT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to TUT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('CPUT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to CPUT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UFS')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UFS</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UMP')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UMP</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('SUN')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to SUN</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('NWU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to NWU</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('WSU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to WSU</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('NMU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to NMU</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UWC')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UWC</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('CUT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to CUT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UFH')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UFH</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('VUT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to VUT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('RU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to RU</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UL')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UL</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UNIZULU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UNIZULU</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('UNIVEN')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to UNIVEN</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('MUT')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to MUT</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('SMU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to SMU</p>
                </div>
                <div class="pre-search flex-center" onclick="filterByUniversity('SPU')">
                    <ion-icon name="search" role="img" class="md hydrated"></ion-icon>
                    <p>close to SPU</p>
                </div>
            </div>
            
        </div>

    </section>


    <section class="city-filters hidden" id="cityFilters">
        
        <div class="inner-container flex-center cities">

            <div class="city johannesburg" onclick="filterByCity('johannesburg')">
                <div class="image bg-image placeholder-24"></div>
                <div class="content">
                    <h1>Johannesburg</h1>
                    <p>Find res in Jo'burg</p>
                </div>
            </div>
            <div class="city cape-town" onclick="filterByCity('cape town')">
                <div class="image bg-image placeholder-24"></div>
                <div class="content">
                    <h1>Cape Town</h1>
                    <p>Find res in Cape Town</p>
                </div>
            </div>
            <div class="city durban" onclick="filterByCity('durban')">
                <div class="image bg-image placeholder-24"></div>
                <div class="content">
                    <h1>Durban</h1>
                    <p>Accommodations Durban</p>
                </div>
            </div>
            <div class="city pretoria" onclick="filterByCity('pretoria')">
                <div class="image bg-image placeholder-24"></div>
                <div class="content">
                    <h1>Pretoria</h1>
                    <p>Find res in Pretoria</p>
                </div>
            </div>
            <div class="city bloemfontein" onclick="filterByCity('bloemfontein')">
                <div class="image bg-image placeholder-24"></div>
                <div class="content">
                    <h1>Bloemfontein</h1>
                    <p>Find res in Bloemfontein</p>
                </div>
            </div>

        </div>

    </section>


    <section class="listings">

        <div class="section-title">
            <h1 id="accommodationTitle">Search results</h1>
        </div>

        <div class="inner-container flex-center" id="accommodations">
            <div class="accommodation" onclick="goTo('accommodation?ai=yplt100trqzt544tvplt314')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc100/IMG-67c031fb4a6a6.jpg')">
                    <p class="rent">From R5,350</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 100)">
                        <ion-icon id="likeIcon100" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>The Richmond</h1>
                    <div class="description">
                        <p>Our student accommodation is modern and fully furnished. Just move in! You can choose to go solo in one of our private studio rooms or make new friends in a...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Auckland Park, Johannesburg</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UJ (APK Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=jhvt99trdwt208tkhnt928')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc99/IMG-67c03263e4eb3.jpg')">
                    <p class="rent">From R5,350</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 99)">
                        <ion-icon id="likeIcon99" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Richmond Central</h1>
                    <div class="description">
                        <p>Our student accommodation is modern and fully furnished. Just move in! You can choose to go solo in one of our private studio rooms or make new friends in a...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Auckland Park, Johannesburg</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UJ (APK Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=lvvt98tciut912tikut312')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc98/IMG-67c033e381687.jpg')">
                    <p class="rent">From R6,150</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 98)">
                        <ion-icon id="likeIcon98" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Kingsway Place</h1>
                    <div class="description">
                        <p>Our student accommodation is modern and fully furnished. Just move in! Enjoy private rooms with shared ensuite kitchen and bathroom facilities. Making friends while enjoying your privacy is what you...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Auckland Park, Johannesburg</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UJ (APK Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=bcst97tipot517twomt985')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc97/IMG-67bc5aef01995.jpg')">
                    <p class="rent">From R5,550</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 97)">
                        <ion-icon id="likeIcon97" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Horizon Heights</h1>
                    <div class="description">
                        <p>Our student accommodation offers modern, fully-furnished options. Choose to go solo in one of our private studio rooms or make new friends in a shared private cluster room with shared...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Auckland Park, Johannesburg</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UJ (APK Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=knut96tnvjt954tbzkt451')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc96/IMG-67bc311c47167.jpg')">
                    <p class="rent">From R5,350</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 96)">
                        <ion-icon id="likeIcon96" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Festivals Edge</h1>
                    <div class="description">
                        <p>Our student accommodation is modern and fully furnished. Just move in! You can choose to go solo in one of our private studio rooms or make new friends in a...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Hatfield, Pretoria</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UP (Hatfield Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=ydkt95tjiyt617trszt346')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc95/IMG-67bc60f81388b.jpg')">
                    <p class="rent">From R5,350</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 95)">
                        <ion-icon id="likeIcon95" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Studios Burnett</h1>
                    <div class="description">
                        <p>Our student accommodation is modern and fully furnished. Just move in! You can choose to go solo in one of our private studio rooms or make new friends in a...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Hatfield, Pretoria</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UP (Hatfield Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=ddut94tmdnt695tngvt695')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc94/IMG-67b8981c24569.jpg')">
                    <p class="rent">From R6,800</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 94)">
                        <ion-icon id="likeIcon94" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Peak Studios</h1>
                    <div class="description">
                        <p>Our student accommodation offers modern, fully furnished options. Choose to go solo in one of our private studio rooms or make new friends in a shared apartment with private rooms...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Observatory, Cape Town</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UCT (Middle Campus)</p>
                    </div>
                </div>
            </div>
        
            <div class="accommodation" onclick="goTo('accommodation?ai=lizt93tzjqt164txuwt620')">
                <div class="image bg-image" style="background-image: url('resources/images/accommodations/acc93/IMG-67b874b8eeafb.jpg')">
                    <p class="rent">From R5,350</p>
                </div>
                <div class="icons flex-right">
                    
                <div class="nsfas flex-center round icon" title="NSFAS Accredited">
                    <img src="resources/images/thirdparty-logos/nsfas.png" alt="NSFAS">
                </div>
            
                    <div class="like-btn flex-center round icon" onclick="likeAccommodation(event, 93)">
                        <ion-icon id="likeIcon93" name="heart-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                </div>
                <div class="content">
                    <div class="ratings flex-left">
                        <div class="stars flex-left">
                            <ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon><ion-icon name="star" class="dull md hydrated" role="img"></ion-icon>
                        </div>
                        <p>0 (0 reviews)</p>
                    </div>
                    <h1>Varsity Studios</h1>
                    <div class="description">
                        <p>Our student accommodation is modern and fully furnished. Just move in! You can choose to go Solo in one of our private studio rooms or make new friends in a...</p>
                    </div>
                    <div class="info flex-left location">
                        <div class="icon flex-center round">
                            <ion-icon name="location-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Hatfield, Pretoria</p>
                    </div>
                    <div class="info flex-left">
                        <div class="icon flex-center round">
                            <ion-icon name="school-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <p>Close to UP (Hatfield Campus)</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="pagination flex-center hidden" id="pagination"><button class="flex-center round primary-btn">1</button></div>

    </section>


    <div class="section invite-landlords">
        <div class="inner-container">
            <h1>List your student accommodation</h1>
            <p>Student24 generates over +60 000 Leads and over R 100 000 000 value in lease agreements.</p>
            <button class="primary-btn" onclick="goTo('landlords')">Learn more</button>
        </div>
    </div>


    <section class="modal auth-modal login-modal flex-center hidden" id="loginModal">

        
<div class="overlay" onclick="display('hide', 'loginModal')"></div>

<div class="modal-content flex-between">

    <button class="close-auth-modal round flex-center" type="button" onclick="display('hide', 'loginModal')">
        <ion-icon name="close" role="img" class="md hydrated"></ion-icon>
    </button>

    <div class="left flex-center" id="loginForm">

        <div class="content">

            <p class="saying">Welcome to Student24</p>

            <div class="heading">
                <h1>Login to your account</h1>
                <p>Enter your details to login to your student/landlord portal.</p>
            </div>

            <div class="nb flex-left hidden" id="nbLogin">
                <ion-icon name="alert-circle-outline" role="img" class="md hydrated"></ion-icon>
                <p id="nbLoginResponse">Incorrect email or password</p>
            </div>

            <div class="form">

                <div class="input-box flex-left low-margin" id="loginEmailBox">
                    <div class="icon flex-right">
                        <ion-icon name="mail-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                    <input type="email" id="loginEmail" class="label-movable">
                    <p>Email address</p>
                    <span id="loginEmailResponse"></span>
                </div>
        
                <div class="input-box flex-left low-margin" id="loginPasswordBox">
                    <div class="icon flex-right">
                        <ion-icon name="finger-print-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                    <input type="password" id="loginPassword" class="label-movable">
                    <p>Password</p>
                    <div class="icon eyes flex-center" onclick="togglePassword('loginPassword')">
                        <ion-icon id="loginPasswordEye" name="eye-off-outline" role="img" class="md hydrated"></ion-icon>
                    </div>
                    <span id="loginPasswordResponse"></span>
                </div>

            </div>

            <div class="action">

                <button class="primary-btn" id="loginBtn" onclick="login()">
                    Login
                </button>

            </div>

            <p class="more-info">
                No account? <span onclick="display('hide', 'loginModal'); display('show', 'registrationModal');">Create one</span>.
                Forgot password? <a href="auth/reset-password">Reset it</a>.
            </p>

        </div>

    </div>

    <div class="right bg-image placeholder-24" id="loginRight">
        <div class="video">
            <video width="100%" autoplay="" muted="" loop="" src="resources/videos/typing.mp4"></video>
        </div>
    </div>

</div>

    </section>


    <section class="modal auth-modal registration-modal flex-center hidden" id="registrationModal">

        
<div class="overlay" onclick="display('hide', 'registrationModal')"></div>

<div class="modal-content flex-between">

    <button class="close-auth-modal round flex-center" type="button" onclick="display('hide', 'registrationModal')">
        <ion-icon name="close" role="img" class="md hydrated"></ion-icon>
    </button>

    <div class="left flex-center" id="registrationForm">

        <div class="content">

            <p class="saying">Welcome to Student24</p>

            <div class="heading">
                <h1>Create an account</h1>
                <p>Enter your details to create your landlord/student account.</p>
            </div>

            <div class="nb flex-left hidden" id="nbRegistration">
                <ion-icon name="alert-circle-outline" role="img" class="md hydrated"></ion-icon>
                <p id="nbRegistrationResponse">Failed to create account</p>
            </div>

            <div class="form">

                <input type="hidden" id="userType">

                <div class="double-input flex-between user-types">
        
                    <label for="landlord" class="user-type flex-left" id="landlordBox">
                        <div class="radio flex-right">
                            <input type="radio" name="userType" id="landlord" value="landlord" oninput="markUserType('landlord')">
                        </div>
                        <p>Landlord</p>
                        <div class="icon flex-right">
                            <ion-icon name="checkmark-circle" role="img" class="md hydrated"></ion-icon>
                        </div>
                    </label>

                    <label for="student" class="user-type flex-left" id="studentBox">
                        <div class="radio flex-right">
                            <input type="radio" name="userType" id="student" value="student" oninput="markUserType('student')">
                        </div>
                        <p>Student</p>
                        <div class="icon flex-right">
                            <ion-icon name="checkmark-circle" role="img" class="md hydrated"></ion-icon>
                        </div>
                    </label>

                </div>

                <div class="double-input flex-between">

                    <div class="input-box flex-left" id="registrationEmailBox">
                        <div class="icon flex-right">
                            <ion-icon name="mail-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <input type="email" id="registrationEmail" class="label-movable">
                        <p>Email address</p>
                        <span id="registrationEmailResponse">We will send an OTP</span>
                    </div>

                    <div class="input-box flex-left hidden" id="whatsappNumberBox">
                        <div class="icon flex-right">
                            <ion-icon name="logo-whatsapp" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <input type="number" id="whatsappNumber" class="label-movable">
                        <p>WhatsApp number</p>
                        <span id="whatsappNumberResponse">For processing bookings</span>
                    </div>

                </div>

                <div class="double-input flex-between">
        
                    <div class="input-box flex-left low-margin" id="registrationPasswordBox">
                        <div class="icon flex-right">
                            <ion-icon name="finger-print-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <input type="password" id="registrationPassword" class="label-movable">
                        <p>Password</p>
                        <div class="icon eyes flex-center" onclick="togglePassword('registrationPassword')">
                            <ion-icon id="registrationPasswordEye" name="eye-off-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <span id="registrationPasswordResponse"></span>
                    </div>

                    <div class="input-box flex-left low-margin" id="registrationPassword2Box">
                        <div class="icon flex-right">
                            <ion-icon name="finger-print-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <input type="password" id="registrationPassword2" class="label-movable" onkeyup="comparePasswords()">
                        <p>Repeat password</p>
                        <div class="icon eyes flex-center" onclick="togglePassword('registrationPassword2')">
                            <ion-icon id="registrationPassword2Eye" name="eye-off-outline" role="img" class="md hydrated"></ion-icon>
                        </div>
                        <span id="registrationPassword2Response"></span>
                    </div>

                </div>

            </div>

            <div class="action">

                <button class="primary-btn" onclick="register()" id="registerBtn">
                    Create account
                </button>

            </div>

            <p class="more-info">
                By registering you agree to our <a href="regulations">terms and conditions</a> and <a href="regulations?type=privacy-policy">privacy policy</a>.
                <br>
                Already have an account? <span onclick="display('hide', 'registrationModal'); display('show', 'loginModal');">Login here</span>.
            </p>

        </div>

    </div>
    
    <div class="right bg-image placeholder-24" id="registrationRight">
        <div class="video">
            <video width="100%" autoplay="" muted="" loop="" src="resources/videos/typing.mp4"></video>
        </div>
    </div>

</div>
    </section>


    <footer>

        
<div class="inner-container flex-between">

    <div class="footer-group group1">

        <div class="brand">
            <img src="resources/images/brand/24.png" alt="logo">
        </div>

    </div>

    <div class="footer-group group2">

        <div class="heading">
            <h2>Quick actions</h2>
        </div>
        <ul>
            <li><a href="landlords">Landlords page</a></li>
            <li><a href="about">About us</a></li>
            <li><a href="contact">Contact us</a></li>
            <li><p onclick="display('show', 'registrationModal')">Create an account</p></li>
            <li><p onclick="display('show','loginModal')">Sign in</p></li>
        </ul>

    </div>

    <div class="footer-group group3">

        <div class="heading">
            <h2>Legal info</h2>
        </div>
        <ul>
            <li><a href="regulations">Terms and conditions</a></li>
            <li><a href="regulations?type=privacy-policy">Privacy policy</a></li>
        </ul>

    </div>

    <div class="footer-group group4">

        <div class="heading">
            <h2>Connect with us</h2>
        </div>
        
        <div class="social-links flex-left">

            <div class="social round flex-center" title="Facebook" onclick="window.open('https://www.facebook.com/student24.co?mibextid=LQQJ4d', '_blank')">
                <ion-icon name="logo-facebook" role="img" class="md hydrated"></ion-icon>
            </div>
            <div class="social round flex-center" title="Instagram" onclick="window.open('https://www.instagram.com/student24.co?igsh=cDgyZm8ycjZ2NHpw', '_blank')">
                <ion-icon name="logo-instagram" role="img" class="md hydrated"></ion-icon>
            </div>
            <!-- <div class="social round flex-center">
                <ion-icon name="logo-linkedin"></ion-icon>
            </div> -->
            <!-- <div class="social round flex-center">
                <ion-icon name="logo-tiktok"></ion-icon>
            </div> -->
            <div class="social round flex-center" title="WhatsApp" onclick="window.open('https://wa.me/+27817177057', '_blank')">
                <ion-icon name="logo-whatsapp" role="img" class="md hydrated"></ion-icon>
            </div>

        </div>

        <div class="subscribe-heading">
            <h2>Subscribe</h2>
            <p>Join our newsletter (Coming soon)</p>
        </div>

        <div class="subscribe flex-left">
            <input type="text" placeholder="Your email...">
            <button class="primary-btn">Subscribe</button>
        </div>

    </div>

</div>

<div class="copyright">
    <div class="inner-container flex-between">
        <p>2025 Student24. All Rights Reserved</p>
        <p style="cursor: pointer" onclick="window.open('https://www.thokozanikubheka.co.za', '_blank')">Powered by TK Web Solutions</p>
    </div>
</div>
    </footer>


    <script src="js/main.js"></script>
    <script src="components/alert/alert.js"></script>
    <script src="components/form/form.js"></script>
    <script src="components/auth/registration/registration.js?v=1.1"></script>
    <script src="components/auth/login/login.js"></script>
    <script src="js/index.js"></script>


</body></html>
#!/usr/bin/env python3
"""
Debug script to save HTML content from Student24.co search results
This will help us understand what the website returns when searching for universities
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
import time
from pathlib import Path
from urllib.parse import quote

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def save_search_html():
    """Save HTML content from Student24.co search results"""
    
    print("🔍 Saving HTML Content from Student24.co Search Results")
    print("=" * 70)
    
    # Create debug output directory
    debug_dir = Path("debug_search_html")
    debug_dir.mkdir(exist_ok=True)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    base_url = "https://student24.co"
    
    # University search terms to test
    university_searches = [
        "University of Johannesburg",
        "UJ", 
        "University of Cape Town",
        "UCT",
        "Wits",
        "UP",
        "Stellenbosch"
    ]
    
    results = {}
    
    try:
        # First, save the main page
        print(f"\n📡 Fetching main page: {base_url}")
        main_response = session.get(base_url, timeout=15)
        
        if main_response.status_code == 200:
            # Save main page HTML
            main_file = debug_dir / "main_page.html"
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write(main_response.text)
            
            # Save prettified version
            soup = BeautifulSoup(main_response.content, 'html.parser')
            main_pretty_file = debug_dir / "main_page_pretty.html"
            with open(main_pretty_file, 'w', encoding='utf-8') as f:
                f.write(soup.prettify())
            
            print(f"✅ Main page saved: {main_file}")
            
            # Look for search form details
            search_input = soup.find('input', {'id': 'location'})
            if search_input:
                print(f"✅ Found search input: {search_input}")
                
                # Find the form containing the search input
                search_form = search_input.find_parent('form')
                if search_form:
                    print(f"✅ Found search form: {search_form.get('action', 'No action')} method={search_form.get('method', 'GET')}")
            
        else:
            print(f"❌ Failed to fetch main page: {main_response.status_code}")
            return False
        
        # Now try different search methods for each university
        for university in university_searches:
            print(f"\n🔍 Testing search for: {university}")
            
            # Method 1: GET request with search parameter
            search_methods = [
                f"{base_url}/?search={quote(university)}",
                f"{base_url}/?q={quote(university)}",
                f"{base_url}/?location={quote(university)}",
                f"{base_url}/accommodation?search={quote(university)}",
                f"{base_url}/accommodation?q={quote(university)}",
                f"{base_url}/accommodation?location={quote(university)}",
            ]
            
            for i, search_url in enumerate(search_methods, 1):
                try:
                    print(f"  Method {i}: {search_url}")
                    
                    response = session.get(search_url, timeout=15)
                    
                    if response.status_code == 200:
                        # Save raw HTML
                        safe_name = university.replace(" ", "_").replace("/", "_")
                        html_file = debug_dir / f"search_{safe_name}_method{i}.html"
                        
                        with open(html_file, 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        
                        # Save prettified version
                        soup = BeautifulSoup(response.content, 'html.parser')
                        pretty_file = debug_dir / f"search_{safe_name}_method{i}_pretty.html"
                        
                        with open(pretty_file, 'w', encoding='utf-8') as f:
                            f.write(soup.prettify())
                        
                        # Analyze content
                        analysis = analyze_search_results(soup, university, search_url)
                        
                        # Save analysis
                        analysis_file = debug_dir / f"search_{safe_name}_method{i}_analysis.txt"
                        with open(analysis_file, 'w', encoding='utf-8') as f:
                            f.write(f"Search URL: {search_url}\n")
                            f.write(f"University: {university}\n")
                            f.write(f"Status: {response.status_code}\n")
                            f.write(f"Content-Length: {len(response.content)} bytes\n\n")
                            f.write("=== ANALYSIS ===\n")
                            for key, value in analysis.items():
                                f.write(f"{key}: {value}\n")
                        
                        print(f"    ✅ Saved: {html_file}")
                        print(f"    📊 Analysis: {analysis.get('accommodation_indicators', 0)} accommodation indicators")
                        print(f"    🏠 Potential listings: {analysis.get('potential_listings', 0)}")
                        
                        # If this method found good results, save it as the best for this university
                        if analysis.get('potential_listings', 0) > 0:
                            best_file = debug_dir / f"BEST_search_{safe_name}.html"
                            with open(best_file, 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            print(f"    🎯 BEST result saved: {best_file}")
                    
                    else:
                        print(f"    ❌ Failed: HTTP {response.status_code}")
                    
                    # Small delay between requests
                    time.sleep(1)
                
                except Exception as e:
                    print(f"    ❌ Error: {e}")
                    continue
            
            # Delay between universities
            time.sleep(2)
        
        # Generate summary report
        print(f"\n" + "=" * 70)
        print("📋 SUMMARY REPORT")
        print("=" * 70)
        
        summary_file = debug_dir / "search_summary.txt"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("Student24.co Search HTML Analysis Summary\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("Files saved in debug_search_html/:\n")
            f.write("- main_page.html - Main Student24.co page\n")
            f.write("- main_page_pretty.html - Prettified main page\n")
            f.write("- search_[university]_method[X].html - Raw search results\n")
            f.write("- search_[university]_method[X]_pretty.html - Prettified search results\n")
            f.write("- search_[university]_method[X]_analysis.txt - Content analysis\n")
            f.write("- BEST_search_[university].html - Best results for each university\n\n")
            
            f.write("Search methods tested:\n")
            f.write("1. /?search=[university]\n")
            f.write("2. /?q=[university]\n")
            f.write("3. /?location=[university]\n")
            f.write("4. /accommodation?search=[university]\n")
            f.write("5. /accommodation?q=[university]\n")
            f.write("6. /accommodation?location=[university]\n\n")
            
            f.write("Universities tested:\n")
            for i, uni in enumerate(university_searches, 1):
                f.write(f"{i}. {uni}\n")
        
        print(f"💾 All files saved to: {debug_dir}")
        print(f"📄 Summary report: {summary_file}")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Review the HTML files to see what the website returns")
        print(f"2. Look for accommodation listing patterns in the HTML")
        print(f"3. Check if JavaScript is loading content dynamically")
        print(f"4. Look for BEST_search_*.html files for promising results")
        print(f"5. Update the scraper based on findings")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during HTML capture: {e}")
        return False

def analyze_search_results(soup, university, search_url):
    """Analyze search results HTML content"""
    
    analysis = {}
    
    # Basic info
    analysis['title'] = soup.title.string if soup.title else "No title"
    analysis['search_url'] = search_url
    analysis['university'] = university
    
    # Look for accommodation indicators
    page_text = soup.get_text().lower()
    accommodation_keywords = [
        'accommodation', 'residence', 'student housing', 'apartment', 
        'room', 'rental', 'booking', 'available', 'per month', 'property'
    ]
    
    keyword_count = sum(page_text.count(keyword) for keyword in accommodation_keywords)
    analysis['accommodation_indicators'] = keyword_count
    
    # Look for potential listing containers
    potential_selectors = [
        'div[class*="accommodation"]',
        'div[class*="listing"]', 
        'div[class*="property"]',
        'div[class*="card"]',
        'article',
        'div[class*="result"]',
        'div[class*="item"]'
    ]
    
    total_potential = 0
    for selector in potential_selectors:
        elements = soup.select(selector)
        if elements:
            total_potential += len(elements)
            analysis[f'selector_{selector}'] = len(elements)
    
    analysis['potential_listings'] = total_potential
    
    # Look for price indicators
    price_patterns = [r'R\s*\d+', r'\d+\s*per\s*month', r'price']
    price_mentions = 0
    for pattern in price_patterns:
        import re
        matches = re.findall(pattern, page_text, re.IGNORECASE)
        price_mentions += len(matches)
    analysis['price_mentions'] = price_mentions
    
    # Look for contact info
    contact_patterns = [r'\d{3}[-.\s]?\d{3}[-.\s]?\d{4}', r'\w+@\w+\.\w+']
    contact_mentions = 0
    for pattern in contact_patterns:
        import re
        matches = re.findall(pattern, page_text)
        contact_mentions += len(matches)
    analysis['contact_mentions'] = contact_mentions
    
    # Check for forms
    forms = soup.find_all('form')
    analysis['form_count'] = len(forms)
    
    # Check for search input
    search_input = soup.find('input', {'id': 'location'})
    analysis['has_search_input'] = search_input is not None
    
    # Look for accommodation links
    links = soup.find_all('a', href=True)
    accommodation_links = [link for link in links 
                          if any(keyword in link.get('href', '').lower() or keyword in link.get_text().lower() 
                                for keyword in ['accommodation', 'residence', 'property'])]
    analysis['accommodation_links'] = len(accommodation_links)
    
    return analysis

if __name__ == '__main__':
    try:
        success = save_search_html()
        if success:
            print("\n🎉 HTML capture completed successfully!")
            print("Review the files in debug_search_html/ to understand the website structure")
        else:
            print("\n❌ HTML capture failed")
    except Exception as e:
        print(f"\n❌ Error during HTML capture: {e}")
        sys.exit(1)

#!/usr/bin/env python3
"""
Debug script to compare what our scraper gets vs what the browser shows
"""

import sys
import os
import requests
from bs4 import BeautifulSoup
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def compare_scraper_vs_browser():
    """Compare what our scraper gets vs browser HTML"""
    
    print("🔍 Comparing Scraper Response vs Browser Response")
    print("=" * 70)
    
    # Create debug output directory
    debug_dir = Path("debug_comparison")
    debug_dir.mkdir(exist_ok=True)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    base_url = "https://student24.co"
    
    try:
        # Get what our scraper sees
        print(f"\n📡 Fetching with scraper: {base_url}")
        scraper_response = session.get(base_url, timeout=15)
        
        if scraper_response.status_code == 200:
            # Save scraper HTML
            scraper_file = debug_dir / "scraper_response.html"
            with open(scraper_file, 'w', encoding='utf-8') as f:
                f.write(scraper_response.text)
            
            # Parse scraper response
            scraper_soup = BeautifulSoup(scraper_response.content, 'html.parser')
            
            # Save prettified version
            scraper_pretty_file = debug_dir / "scraper_response_pretty.html"
            with open(scraper_pretty_file, 'w', encoding='utf-8') as f:
                f.write(scraper_soup.prettify())
            
            print(f"✅ Scraper response saved: {scraper_file}")
            
            # Analyze scraper response
            scraper_analysis = analyze_html_content(scraper_soup, "scraper")
            
            # Load browser response for comparison
            browser_file = Path("browser_response.html")
            if browser_file.exists():
                print(f"\n📖 Loading browser response: {browser_file}")
                
                with open(browser_file, 'r', encoding='utf-8') as f:
                    browser_html = f.read()
                
                browser_soup = BeautifulSoup(browser_html, 'html.parser')
                browser_analysis = analyze_html_content(browser_soup, "browser")
                
                # Compare the two
                print(f"\n📊 COMPARISON ANALYSIS:")
                print("=" * 70)
                
                comparison = compare_analyses(scraper_analysis, browser_analysis)
                
                # Save comparison report
                comparison_file = debug_dir / "comparison_report.txt"
                with open(comparison_file, 'w', encoding='utf-8') as f:
                    f.write("Scraper vs Browser HTML Comparison\n")
                    f.write("=" * 50 + "\n\n")
                    
                    f.write("SCRAPER ANALYSIS:\n")
                    for key, value in scraper_analysis.items():
                        f.write(f"  {key}: {value}\n")
                    
                    f.write("\nBROWSER ANALYSIS:\n")
                    for key, value in browser_analysis.items():
                        f.write(f"  {key}: {value}\n")
                    
                    f.write("\nCOMPARISON:\n")
                    for key, value in comparison.items():
                        f.write(f"  {key}: {value}\n")
                
                print(f"💾 Comparison report saved: {comparison_file}")
                
                # Show key differences
                print(f"\n🔍 KEY DIFFERENCES:")
                for key, value in comparison.items():
                    if 'difference' in key.lower() or 'missing' in key.lower():
                        print(f"  {key}: {value}")
                
                # Check for accommodations container specifically
                print(f"\n🏠 ACCOMMODATIONS CONTAINER CHECK:")
                
                scraper_accommodations = scraper_soup.find('div', {'id': 'accommodations'})
                browser_accommodations = browser_soup.find('div', {'id': 'accommodations'})
                
                print(f"  Scraper has accommodations container: {scraper_accommodations is not None}")
                print(f"  Browser has accommodations container: {browser_accommodations is not None}")
                
                if scraper_accommodations:
                    scraper_acc_divs = scraper_accommodations.find_all('div', class_='accommodation')
                    print(f"  Scraper accommodation divs: {len(scraper_acc_divs)}")
                else:
                    print(f"  Scraper: No accommodations container found")
                
                if browser_accommodations:
                    browser_acc_divs = browser_accommodations.find_all('div', class_='accommodation')
                    print(f"  Browser accommodation divs: {len(browser_acc_divs)}")
                    
                    if browser_acc_divs:
                        print(f"\n📋 BROWSER ACCOMMODATIONS FOUND:")
                        for i, acc_div in enumerate(browser_acc_divs[:3], 1):  # Show first 3
                            title_elem = acc_div.find('h1')
                            title = title_elem.get_text().strip() if title_elem else "No title"
                            print(f"    {i}. {title}")
                else:
                    print(f"  Browser: No accommodations container found")
                
                # Recommendations
                print(f"\n💡 RECOMMENDATIONS:")
                
                if not scraper_accommodations and browser_accommodations:
                    print("  🔧 The accommodations are loaded dynamically after page load")
                    print("  🔧 Consider using Selenium to wait for dynamic content")
                    print("  🔧 Or find the API endpoint that loads the accommodations")
                
                elif scraper_accommodations and not browser_accommodations:
                    print("  ⚠️  Unexpected: Scraper found container but browser didn't")
                
                elif not scraper_accommodations and not browser_accommodations:
                    print("  ⚠️  Neither found accommodations container - check HTML structure")
                
                else:
                    print("  ✅ Both found accommodations container - check extraction logic")
                
            else:
                print(f"❌ Browser response file not found: {browser_file}")
                print("Please create browser_response.html with the browser HTML")
        
        else:
            print(f"❌ Failed to fetch with scraper: {scraper_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        return False

def analyze_html_content(soup, source_name):
    """Analyze HTML content and return key metrics"""
    
    analysis = {}
    
    # Basic info
    analysis['source'] = source_name
    analysis['title'] = soup.title.string if soup.title else "No title"
    
    # Content size
    page_text = soup.get_text()
    analysis['content_length'] = len(page_text)
    analysis['html_length'] = len(str(soup))
    
    # Look for key elements
    analysis['has_accommodations_container'] = soup.find('div', {'id': 'accommodations'}) is not None
    analysis['has_search_input'] = soup.find('input', {'id': 'location'}) is not None
    
    # Count accommodation elements
    accommodations_container = soup.find('div', {'id': 'accommodations'})
    if accommodations_container:
        acc_divs = accommodations_container.find_all('div', class_='accommodation')
        analysis['accommodation_divs_count'] = len(acc_divs)
    else:
        analysis['accommodation_divs_count'] = 0
    
    # Look for accommodation keywords
    accommodation_keywords = ['accommodation', 'residence', 'student housing', 'rent']
    keyword_count = sum(page_text.lower().count(keyword) for keyword in accommodation_keywords)
    analysis['accommodation_keywords'] = keyword_count
    
    # Look for price mentions
    import re
    price_mentions = len(re.findall(r'R\s*\d+', page_text))
    analysis['price_mentions'] = price_mentions
    
    # Count scripts
    scripts = soup.find_all('script')
    analysis['script_count'] = len(scripts)
    
    # Look for specific scripts
    accommodation_js = soup.find('script', src=lambda x: x and 'accommodation.js' in x)
    analysis['has_accommodation_js'] = accommodation_js is not None
    
    return analysis

def compare_analyses(scraper_analysis, browser_analysis):
    """Compare two HTML analyses"""
    
    comparison = {}
    
    # Compare key metrics
    for key in scraper_analysis:
        if key in browser_analysis:
            scraper_val = scraper_analysis[key]
            browser_val = browser_analysis[key]
            
            if isinstance(scraper_val, (int, float)) and isinstance(browser_val, (int, float)):
                difference = scraper_val - browser_val
                comparison[f'{key}_difference'] = difference
            elif scraper_val != browser_val:
                comparison[f'{key}_differs'] = f"Scraper: {scraper_val}, Browser: {browser_val}"
            else:
                comparison[f'{key}_same'] = True
    
    # Check for missing elements
    scraper_keys = set(scraper_analysis.keys())
    browser_keys = set(browser_analysis.keys())
    
    missing_in_scraper = browser_keys - scraper_keys
    missing_in_browser = scraper_keys - browser_keys
    
    if missing_in_scraper:
        comparison['missing_in_scraper'] = list(missing_in_scraper)
    
    if missing_in_browser:
        comparison['missing_in_browser'] = list(missing_in_browser)
    
    return comparison

if __name__ == '__main__':
    try:
        success = compare_scraper_vs_browser()
        if success:
            print("\n🎉 Comparison completed successfully!")
            print("Review the files in debug_comparison/ for detailed analysis")
        else:
            print("\n❌ Comparison failed")
    except Exception as e:
        print(f"\n❌ Error during comparison: {e}")
        sys.exit(1)
